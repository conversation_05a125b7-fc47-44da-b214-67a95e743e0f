{
  "include": [
    "typings.d.ts",
    "src/**/*",
    "node_modules/**/*.d.ts",
    "src/__test__/**/*"
  ],
  "compilerOptions": {
    "types": [
      "node",
      "vitest"
    ],
    "strict": false,
    "declaration": true,
    "declarationMap": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "lib": [
      "esnext",
      "es6",
      "es5",
      "dom"
    ],
    "noUnusedLocals": false,
    "noUnusedParameters": true,
    "skipLibCheck": true,
    "target": "esnext",
    "emitDeclarationOnly": true,
    "declarationDir": "dist/types",
    "resolveJsonModule": true,
    "forceConsistentCasingInFileNames": true,
    "importHelpers": true,
    "baseUrl": "./",
    // 根目录
    "paths": {
      "@/*": [
        "src/*"
      ]
    }
  }
}
