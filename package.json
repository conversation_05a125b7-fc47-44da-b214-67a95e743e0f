{"name": "@iam/init-sdk", "version": "0.1.9", "type": "module", "description": "iam-next-sdk", "module": "dist/index.esm.min.js", "types": "dist/types/index.d.ts", "scripts": {"dev": "cross-env IAM_ENV=dev father dev", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "change": "pnpm hoyo-changeset", "prelease": "pnpm hoyo-changeset pre enter", "prelease:exit": "pnpm hoyo-changeset pre exit", "bump": "pnpm hoyo-changeset version", "build:all": "pnpm lint && pnpm build && pnpm build:compatible && pnpm build:esm", "build": "cross-env TARGET=umd pnpm rollup -c", "build:compatible": "cross-env TARGET=compatible pnpm rollup -c", "build:esm": "cross-env TARGET=esm pnpm rollup -c", "pub:core:ci": "pnpm build && cross-env npm_config_registry=https://npm.mihoyo.com/ pnpm hoyo-changeset publish", "pub:core:test:ci:dev": "pnpm build:all && cross-env npm_config_registry=https://info-npm-core-test.mihoyo.com/ pnpm hoyo-changeset publish", "lint": "eslint --fix"}, "keywords": ["iam"], "authors": ["<EMAIL>"], "license": "unlicense", "files": ["dist", "CHANGELOG.md", "typings.d.ts"], "devDependencies": {"@babel/cli": "^7.24.7", "@babel/core": "^7.24.7", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/plugin-transform-typeof-symbol": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@changesets/cli": "^2.27.1", "@eslint/js": "^9.6.0", "@hoyo/changeset": "^1.0.1", "@hoyowave/jsapi": "0.1.5-beta.1", "@rollup/plugin-babel": "^5.0.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/fs-extra": "^11.0.4", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@umijs/test": "^4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "bannerjs": "^3.0.2", "core-js": "^3.37.1", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^9.6.0", "eslint-plugin-es": "^4.1.0", "eslint-plugin-react": "^7.34.3", "fs-extra": "^11.2.0", "globals": "^15.7.0", "happy-dom": "^18.0.1", "jsdom": "^24.1.0", "prettier": "^3.2.5", "regenerator-runtime": "^0.14.1", "rollup": "^2.79.1", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.12.0", "ts-node": "^10.9.2", "tslib": "^2.6.3", "turbo": "^1.13.2", "typescript": "^4", "typescript-eslint": "^7.14.1", "util": "^0.12.5", "vitest": "^3.2.4"}, "dependencies": {"@babel/runtime": "^7.24.4", "@rollup/plugin-terser": "^0.4.4", "whatwg-fetch": "^3.6.20"}}