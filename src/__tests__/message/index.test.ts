import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest'
import {onPostMessage, replyOnPostMessage, requestPostMessage, sendPostMessage, unsubscribeAll} from '@/message'
import { getRandomId } from '@/message/utils'

// Mock utils module at the top level
vi.mock('@/message/utils', () => ({
  getRandomId: vi.fn()
}))

describe('Message Module', () => {
  let mockIframe: HTMLIFrameElement
  let mockWindow: Window

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock getRandomId to return a predictable value
    vi.mocked(getRandomId).mockReturnValue('mock-id-123')

    // Mock iframe
    mockIframe = {
      contentWindow: {
        postMessage: vi.fn()
      } as any
    } as HTMLIFrameElement

    // Mock window
    mockWindow = {
      postMessage: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    } as any

    // Mock global window
    Object.defineProperty(window, 'addEventListener', {
      value: vi.fn(),
      writable: true
    })
    Object.defineProperty(window, 'removeEventListener', {
      value: vi.fn(),
      writable: true
    })
  })

  afterEach(() => {
    unsubscribeAll()
  })

  describe('sendPostMessage', () => {
    it('应该通过iframe发送消息', () => {
      const eventName = 'test'
      const data = 'hello'
      const targetOrigin = 'https://example.com'

      sendPostMessage({
        target: mockIframe.contentWindow,
        eventName,
        data,
        targetOrigin
      })

      expect(mockIframe.contentWindow!.postMessage).toHaveBeenCalledWith(
        {
          MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
          eventName,
          data,
          error: undefined
        },
        targetOrigin
      )
    })

    it('应该通过window发送消息', () => {
      const eventName = 'test'
      const data = 'hello'
      const targetOrigin = 'https://example.com'

      sendPostMessage({
        target: mockWindow,
        eventName,
        data,
        targetOrigin
      })

      expect(mockWindow.postMessage).toHaveBeenCalledWith(
        {
          MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
          eventName,
          data,
          error: undefined
        },
        targetOrigin
      )
    })

    it('应该处理无效target', () => {
      const eventName = 'test'
      const data = 'hello'
      const targetOrigin = 'https://example.com'

      expect(() => {
        sendPostMessage({
          target: null as any,
          eventName,
          data,
          targetOrigin
        })
      }).toThrow('Cannot read properties of null')
    })
  })

  describe('onPostMessage', () => {
    it('应该添加消息监听器', () => {
      const callback = vi.fn()
      const eventName = 'testEvent'

      onPostMessage({ eventName, callback })

      expect(window.addEventListener).toHaveBeenCalledWith(
        'message',
        expect.any(Function),
        false
      )
    })

    it('应该在收到匹配消息时触发回调', () => {
      const callback = vi.fn()
      const eventName = 'testEvent'
      const messageData = {
        MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
        eventName,
        data: 'test'
      }

      onPostMessage({ eventName, callback })

      // 模拟消息事件
      const messageEvent = new MessageEvent('message', {
        data: messageData,
        origin: 'https://example.com'
      })

      const addedListener = vi.mocked(window.addEventListener).mock.calls[0][1] as EventListener
      addedListener(messageEvent)

      expect(callback).toHaveBeenCalledWith(messageEvent, 'test')
    })

    it('应该忽略不匹配的消息', () => {
      const callback = vi.fn()
      const eventName = 'testEvent'
      const messageData = {
        MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
        eventName: 'otherEvent',
        data: 'test'
      }

      onPostMessage({ eventName, callback })

      // 模拟消息事件
      const messageEvent = new MessageEvent('message', {
        data: messageData,
        origin: 'https://example.com'
      })

      const addedListener = vi.mocked(window.addEventListener).mock.calls[0][1] as EventListener
      addedListener(messageEvent)

      expect(callback).not.toHaveBeenCalled()
    })

    it('应该忽略没有MESSAGE_IDENTIFIER的消息', () => {
      const callback = vi.fn()
      const eventName = 'testEvent'
      const messageData = {
        eventName,
        data: 'test'
      }

      onPostMessage({ eventName, callback })

      // 模拟消息事件
      const messageEvent = new MessageEvent('message', {
        data: messageData,
        origin: 'https://example.com'
      })

      const addedListener = vi.mocked(window.addEventListener).mock.calls[0][1] as EventListener
      addedListener(messageEvent)

      expect(callback).not.toHaveBeenCalled()
    })

    it('应该处理错误回调', () => {
      const callback = vi.fn()
      const onError = vi.fn()
      const eventName = 'testEvent'
      const error = 'test error'
      const messageData = {
        MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
        eventName,
        error,
        data: 'test'
      }

      onPostMessage({ eventName, callback, onError })

      // 模拟消息事件
      const messageEvent = new MessageEvent('message', {
        data: messageData,
        origin: 'https://example.com'
      })

      const addedListener = vi.mocked(window.addEventListener).mock.calls[0][1] as EventListener
      addedListener(messageEvent)

      expect(onError).toHaveBeenCalledWith(messageEvent, error)
      expect(callback).not.toHaveBeenCalled()
    })

    it('应该返回取消订阅函数', () => {
      const callback = vi.fn()
      const eventName = 'testEvent'

      const unsubscribe = onPostMessage({ eventName, callback })

      expect(typeof unsubscribe).toBe('function')

      // 调用取消订阅
      unsubscribe()

      expect(window.removeEventListener).toHaveBeenCalledWith(
        'message',
        expect.any(Function),
        false
      )
    })
  })

  describe('requestPostMessage', () => {
    it('应该发送请求并等待响应', async () => {
      const data = { payload: 'test' }
      const eventName = 'request'
      const targetOrigin = 'https://example.com'

      const promise = requestPostMessage({
        target: mockIframe.contentWindow,
        eventName,
        data,
        targetOrigin
      })

      // 验证请求消息被发送
      expect(mockIframe.contentWindow!.postMessage).toHaveBeenCalledWith(
        {
          MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
          eventName,
          data: { ...data, requestId: 'mock-id-123' },
          error: undefined
        },
        targetOrigin
      )

      // 模拟响应
      const responseData = {
        MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
        eventName: 'request_mock-id-123',
        data: 'success'
      }
      const messageEvent = new MessageEvent('message', {
        data: responseData,
        origin: targetOrigin
      })
      
      // 获取添加的监听器并触发响应
      const listeners = vi.mocked(window.addEventListener).mock.calls
      const responseListener = listeners.find(call => call[0] === 'message')?.[1] as EventListener
      if (responseListener) {
        responseListener(messageEvent)
      }

      const result = await promise
      expect(result).toEqual('success')
    })

    it('应该在没有target时reject', async () => {
      const data = 'test'
      const eventName = 'request'

      const promise = requestPostMessage({
        target: null,
        eventName,
        data
      })

      await expect(promise).rejects.toEqual({ message: 'No target' })
    })

    it('应该在收到错误响应时reject', async () => {
      const data = 'test'
      const eventName = 'request'
      const targetOrigin = 'https://example.com'
      const error = 'test error'

      const promise = requestPostMessage({
        target: mockIframe.contentWindow,
        eventName,
        data,
        targetOrigin
      })

      // 模拟错误响应
      const responseData = {
        MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
        eventName: 'request_mock-id-123',
        error
      }
      const messageEvent = new MessageEvent('message', {
        data: responseData,
        origin: targetOrigin
      })
      
      // 获取添加的监听器并触发错误响应
      const listeners = vi.mocked(window.addEventListener).mock.calls
      const responseListener = listeners.find(call => call[0] === 'message')?.[1] as EventListener
      if (responseListener) {
        responseListener(messageEvent)
      }

      await expect(promise).rejects.toEqual(error)
    })
  })

  describe('replyOnPostMessage', () => {
    it('应该监听消息并自动回复', () => {
      const eventName = 'testRequest'
      const callback = vi.fn().mockResolvedValue({ status: 'ok' })

      replyOnPostMessage({ eventName, callback })

      expect(window.addEventListener).toHaveBeenCalledWith(
        'message',
        expect.any(Function),
        false
      )
    })

    it('应该在收到消息时发送回复', async () => {
      const eventName = 'testRequest'
      const callback = vi.fn().mockResolvedValue({ status: 'ok' })
      const origin = 'https://example.com'

      replyOnPostMessage({ eventName, callback })

      // 模拟请求消息
      const messageEvent = new MessageEvent('message', {
        data: {
          MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
          eventName,
          data: { requestId: '123', payload: 'test' }
        },
        origin,
        source: mockWindow
      })

      const addedListener = vi.mocked(window.addEventListener).mock.calls[0][1] as EventListener
      await addedListener(messageEvent)

      // 等待异步回调完成
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(callback).toHaveBeenCalledWith(messageEvent, { payload: 'test' })
      expect(mockWindow.postMessage).toHaveBeenCalledWith(
        {
          MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
          eventName: `${eventName}_123`,
          data: { status: 'ok' },
          error: undefined
        },
        '*'
      )
    })

    it('应该在回调抛出错误时发送错误响应', async () => {
      const eventName = 'testRequest'
      const error = new Error('callback error')
      const callback = vi.fn().mockRejectedValue(error)
      const origin = 'https://example.com'

      replyOnPostMessage({ eventName, callback })

      // 模拟请求消息
      const messageEvent = new MessageEvent('message', {
        data: {
          MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
          eventName,
          data: { requestId: '456', payload: 'test' }
        },
        origin,
        source: mockWindow
      })

      const addedListener = vi.mocked(window.addEventListener).mock.calls[0][1] as EventListener
      await addedListener(messageEvent)

      // 等待异步回调完成
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(callback).toHaveBeenCalledWith(messageEvent, { payload: 'test' })
      expect(mockWindow.postMessage).toHaveBeenCalledWith(
        {
          MESSAGE_IDENTIFIER: 'UNIQUE_MESSAGE_CHANNEL_IDENTIFIER',
          eventName: `${eventName}_456`,
          data: undefined,
          error
        },
        '*'
      )
    })

    it('应该返回取消订阅函数', () => {
      const eventName = 'testRequest'
      const callback = vi.fn().mockResolvedValue({ status: 'ok' })

      const unsubscribe = replyOnPostMessage({ eventName, callback })

      expect(typeof unsubscribe).toBe('function')
    })
  })

  describe('unsubscribeAll', () => {
    it('应该移除所有监听器', () => {
      const callback1 = vi.fn()
      const callback2 = vi.fn()

      onPostMessage({ eventName: 'event1', callback: callback1 })
      onPostMessage({ eventName: 'event2', callback: callback2 })

      unsubscribeAll()

      expect(window.removeEventListener).toHaveBeenCalledWith(
        'message',
        expect.any(Function),
        false
      )
      // 应该被调用两次，移除两个监听器
      expect(window.removeEventListener).toHaveBeenCalledTimes(2)
    })

    it('应该清空监听器列表', () => {
      onPostMessage({ eventName: 'event1', callback: vi.fn() })
      onPostMessage({ eventName: 'event2', callback: vi.fn() })

      expect(vi.mocked(window.addEventListener).mock.calls.length).toBeGreaterThan(0)

      unsubscribeAll()

      // 再次添加监听器应该重新开始计数
      vi.clearAllMocks()
      onPostMessage({ eventName: 'event3', callback: vi.fn() })

      expect(vi.mocked(window.addEventListener).mock.calls.length).toBe(1)
    })
  })
})
