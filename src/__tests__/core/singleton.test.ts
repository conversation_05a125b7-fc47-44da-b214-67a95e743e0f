import { describe, it, expect } from 'vitest'
import Singleton from '@/core/singleton'

describe('Singleton', () => {
  it('应该正确初始化', () => {
    const singleton = new Singleton()
    expect(singleton.isExecuting).toBe(false)
  })

  it('应该能够设置和获取执行状态', () => {
    const singleton = new Singleton()
    
    singleton.isExecuting = true
    expect(singleton.isExecuting).toBe(true)
    
    singleton.isExecuting = false
    expect(singleton.isExecuting).toBe(false)
  })

  it('应该确保同一个类的实例共享状态', () => {
    class TestSingleton extends Singleton {
      static instance: TestSingleton | null = null
      
      static getInstance() {
        if (!this.instance) {
          this.instance = new TestSingleton()
        }
        return this.instance
      }
    }
    
    const instance1 = TestSingleton.getInstance()
    const instance2 = TestSingleton.getInstance()
    
    expect(instance1).toBe(instance2)
    
    instance1.isExecuting = true
    expect(instance2.isExecuting).toBe(true)
  })
})