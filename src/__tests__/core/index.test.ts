import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest'
import {instance, Main} from '@/core'
import type {Ctx} from '@/types'
import * as iframe from '@/core/iframe'
import * as message from '@/message'

// Mock dependencies
vi.mock('@/core/iframe', () => ({
  render: vi.fn().mockResolvedValue({}),
  destroy: vi.fn()
}))

vi.mock('@/message', () => ({
  unsubscribeAll: vi.fn()
}))

vi.mock('@/utils', () => ({
  getIsWaveEnv: vi.fn().mockReturnValue(false),
  getMoatClientId: vi.fn().mockReturnValue('moat-client-id')
}))

// Create a proper Services mock
const mockServices = {
  sessionCheck: vi.fn().mockResolvedValue({code: 0}),
  shimGetCorsTicket: vi.fn().mockResolvedValue({code: 0, data: {ticket: 'test-ticket'}}),
  shimTicketLogin: vi.fn().mockResolvedValue({code: 0}),
  waveSilenceLogin: vi.fn().mockResolvedValue({code: 0, data: {ticket: 'wave-ticket'}}),
  syncSessionToIam: vi.fn().mockResolvedValue({code: 0})
}

vi.mock('@/services', () => ({
  default: vi.fn().mockImplementation(() => mockServices)
}))

vi.mock('@/core/listeners', () => ({
  default: vi.fn().mockResolvedValue({})
}))

describe('Core Module', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Clean up singleton instance
    if (instance) {
      instance.clean()
    }
  })

  describe('Main Class', () => {
    describe('constructor', () => {
      it('应该正确初始化Main实例', () => {
        const main = new Main()
        expect(main).toBeInstanceOf(Main)
      })
    })

    describe('clean', () => {
      it('应该重置所有状态', () => {
        const main = new Main()
        main.clean()

        // Verify that destroy and unsubscribeAll are called
        expect(vi.mocked(iframe.destroy)).toHaveBeenCalled()
        expect(vi.mocked(message.unsubscribeAll)).toHaveBeenCalled()
      })
    })

    describe('sessionCheck', () => {
      it('应该在Wave环境中直接返回ctx', async () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: true,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.sessionCheck(ctx)
        expect(result).toBe(ctx)
        expect(mockServices.sessionCheck).not.toHaveBeenCalled()
      })

      it('应该在已登录时直接返回ctx', async () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: true,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.sessionCheck(ctx)
        expect(result).toBe(ctx)
        expect(mockServices.sessionCheck).not.toHaveBeenCalled()
      })

      it('应该在会话检查成功时设置isLogin为true', async () => {
        mockServices.sessionCheck.mockResolvedValue({code: 0})

        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.sessionCheck(ctx)
        expect(result.isLogin).toBe(true)
        expect(mockServices.sessionCheck).toHaveBeenCalled()
      })

      it('应该在会话检查失败时设置isLogin为false', async () => {
        mockServices.sessionCheck.mockResolvedValue({code: -1})

        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.sessionCheck(ctx)
        expect(result.isLogin).toBe(false)
        expect(mockServices.sessionCheck).toHaveBeenCalled()
      })

      it('应该在需要二次验证时设置scene为second_mfa', async () => {
        mockServices.sessionCheck.mockResolvedValue({code: -5})

        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.sessionCheck(ctx)
        expect(result.isLogin).toBe(false)
        expect(result.config.scene).toBe('second_mfa')
        expect(mockServices.sessionCheck).toHaveBeenCalled()
      })
    })

    describe('checkArgs', () => {
      it('应该在缺少config时抛出错误', () => {
        const main = new Main()
        // @ts-expect-error - 故意设置为undefined进行测试
        main.ctx = {config: undefined}

        expect(() => main.checkArgs()).toThrow('config is required')
      })

      it('应该在缺少clientId时抛出错误', () => {
        const main = new Main()
        // @ts-expect-error - 故意设置为undefined进行测试
        main.ctx = {
          config: {
            env: 'test',
            lang: 'zh-CN'
          } as any
        }

        expect(() => main.checkArgs()).toThrow('clientId is required')
      })

      it('应该在缺少env时抛出错误', () => {
        const main = new Main()
        // @ts-expect-error - 故意设置为undefined进行测试
        main.ctx = {
          config: {
            clientId: 'test-client',
            lang: 'zh-CN'
          } as any
        }

        expect(() => main.checkArgs()).toThrow('env is required')
      })

      it('应该在参数完整时返回true', () => {
        const main = new Main()
        // @ts-expect-error - 故意设置进行测试
        main.ctx = {
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        expect(main.checkArgs()).toBe(true)
      })
    })

    describe('checkRender', () => {
      it('应该在showPage为false时失败', () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN',
            showPage: false
          }
        }

        expect(() => main.checkRender(ctx)).toThrow('showPage is false')
      })

      it('应该在showPage为true时成功', () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN',
            showPage: true
          }
        }

        expect(() => main.checkRender(ctx)).not.toThrow()
      })

      it('应该在未设置showPage时默认成功', () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        expect(() => main.checkRender(ctx)).not.toThrow()
      })
    })

    describe('SyncSessionToMoat', () => {
      it('应该在Wave环境中直接返回ctx', async () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: true,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.SyncSessionToMoat(ctx)
        expect(result).toBe(ctx)
        expect(mockServices.shimGetCorsTicket).not.toHaveBeenCalled()
      })

      it('应该在已登录时直接返回ctx', async () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: true,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.SyncSessionToMoat(ctx)
        expect(result).toBe(ctx)
        expect(mockServices.shimGetCorsTicket).not.toHaveBeenCalled()
      })

      it('应该在获取票据成功时进行登录', async () => {
        mockServices.shimGetCorsTicket.mockResolvedValue({code: 0, data: {ticket: 'test-ticket'}})
        mockServices.shimTicketLogin.mockResolvedValue({code: 0})

        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.SyncSessionToMoat(ctx)
        expect(result.isLogin).toBe(true)
        expect(mockServices.shimGetCorsTicket).toHaveBeenCalled()
        expect(mockServices.shimTicketLogin).toHaveBeenCalledWith('test-ticket')
      })
    })

    describe('waveSilenceLogin', () => {
      it('应该在已登录时直接返回ctx', async () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: true,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.waveSilenceLogin(ctx)
        expect(result).toBe(ctx)
        expect(mockServices.waveSilenceLogin).not.toHaveBeenCalled()
      })

      it('应该在非Wave环境中直接返回ctx', async () => {
        const main = new Main()
        const ctx: Ctx = {
          isWaveEnv: false,
          isLogin: false,
          isRender: false,
          errObj: {},
          config: {
            clientId: 'test-client',
            env: 'test',
            lang: 'zh-CN'
          }
        }

        const result = await main.waveSilenceLogin(ctx)
        expect(result).toBe(ctx)
        expect(mockServices.waveSilenceLogin).not.toHaveBeenCalled()
      })
    })
  })

  describe('Singleton Instance', () => {
    it('应该创建单例实例', () => {
      expect(instance).toBeDefined()
    })

    it('应该在多次访问时返回同一个实例', () => {
      const instance1 = instance
      const instance2 = instance
      expect(instance1).toBe(instance2)
    })
  })
})
