import {describe, expect, it, vi} from 'vitest'
import Callback from '@/core/callback'
import {IError} from "@/types";

describe('Callback', () => {
  it('应该正确初始化回调函数', () => {
    const mockCallback = vi.fn()
    const callback = new Callback(mockCallback)

    expect(callback).toBeInstanceOf(Callback)
  })

  it('应该在缺少回调函数时抛出错误', () => {
    expect(() => {
      new Callback(null as any)
    }).toThrow('callback is required')
  })

  it('应该在成功时调用回调函数', () => {
    const mockCallback = vi.fn()
    const callback = new Callback(mockCallback)

    callback.success()

    expect(mockCallback).toHaveBeenCalledWith({
      onSuccess: expect.any(Function),
      onError: expect.any(Function),
      onFail: expect.any(Function)
    })
  })

  it('应该在错误时调用回调函数', () => {
    const mockCallback = vi.fn()
    const callback = new Callback(mockCallback)
    const errorInfo: IError = {code: 500, message: 'Internal Error', browser: true}

    callback.error(errorInfo)

    expect(mockCallback).toHaveBeenCalledWith({
      onSuccess: expect.any(Function),
      onError: expect.any(Function),
      onFail: expect.any(Function)
    })
  })

  it('应该在失败时调用回调函数', () => {
    const mockCallback = vi.fn()
    const callback = new Callback(mockCallback)

    callback.fail('Login failed')

    expect(mockCallback).toHaveBeenCalledWith({
      onSuccess: expect.any(Function),
      onError: expect.any(Function),
      onFail: expect.any(Function)
    })
  })

  it('应该正确处理成功回调', () => {
    const mockCallback = vi.fn()
    const callback = new Callback(mockCallback)

    callback.success()

    const callbackObj = mockCallback.mock.calls[0][0]
    const mockSuccessCallback = vi.fn()

    callbackObj.onSuccess(mockSuccessCallback)
    expect(mockSuccessCallback).toHaveBeenCalled()
  })

  it('应该正确处理错误回调', () => {
    const mockCallback = vi.fn()
    const callback = new Callback(mockCallback)
    const errorInfo: IError = {code: 500, message: 'Internal Error', browser: true}

    callback.error(errorInfo)

    const callbackObj = mockCallback.mock.calls[0][0]
    const mockErrorCallback = vi.fn()

    callbackObj.onError(mockErrorCallback)
    expect(mockErrorCallback).toHaveBeenCalledWith(errorInfo)
  })

  it('应该正确处理失败回调', () => {
    const mockCallback = vi.fn()
    const callback = new Callback(mockCallback)
    const failInfo = 'Login failed'

    callback.fail(failInfo)

    const callbackObj = mockCallback.mock.calls[0][0]
    const mockFailCallback = vi.fn()

    callbackObj.onFail(mockFailCallback)
    expect(mockFailCallback).toHaveBeenCalledWith(failInfo)
  })
})
