import {describe, expect, it} from 'vitest'
import AppConfig from '@/app-config'
import {AppConfigArg} from '@/types'

describe('App Config Module', () => {
  describe('AppConfig', () => {
    it('应该正确初始化配置', () => {
      const config: AppConfigArg = {
        clientId: 'test-client',
        env: 'test'
      }

      const appConfig = new AppConfig('test', config)
      expect(appConfig).toBeInstanceOf(AppConfig)
    })

    it('应该在缺少env时抛出错误', () => {
      expect(() => {
        new AppConfig('' as any)
      }).toThrow('env is required')
    })

    it('应该生成包含环境信息的配置', () => {
      const config: AppConfigArg = {
        clientId: 'test-client'
      }

      const appConfig = new AppConfig('test', config)
      const result = appConfig.getAppConfig()

      expect(result).toMatchObject({
        clientId: 'test-client',
        env: 'test',
        iamNext: 1
      })
      expect(result.baseURL).toBeDefined()
    })

    it('应该处理不同环境', () => {
      const envs = ['test', 'uat', 'prod', 'pp'] as const

      envs.forEach(env => {
        const config: AppConfigArg = {
          clientId: 'test-client'
        }

        const appConfig = new AppConfig(env, config)
        const result = appConfig.getAppConfig()

        expect(result.env).toBe(env)
        expect(result.iamNext).toBe(1)
      })
    })

    it('检测不传入环境报错', () => {
      const config: AppConfigArg = {
        clientId: 'test-client'
      }
      expect(() => {
        new AppConfig(null as any, config)
      }).toThrow('env is required')
    })

    it('应该合并用户配置', () => {
      const config: AppConfigArg = {
        clientId: 'test-client',
        customProp: 'custom-value'
      }

      const appConfig = new AppConfig('test', config)
      const result = appConfig.getAppConfig()

      expect(result).toMatchObject({
        clientId: 'test-client',
        customProp: 'custom-value',
        env: 'test',
        iamNext: 1
      })
    })

    it('应该在没有配置时工作', () => {
      const appConfig = new AppConfig('test')
      const result = appConfig.getAppConfig()

      expect(result).toMatchObject({
        env: 'test',
        iamNext: 1
      })
      expect(result.baseURL).toBeDefined()
    })
  })
})
