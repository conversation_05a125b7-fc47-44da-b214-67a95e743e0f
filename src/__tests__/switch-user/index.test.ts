import { describe, it, expect, beforeEach, vi } from 'vitest'
import SwitchUser from '@/switch-user'
import type { Env } from '@/types'

// Mock Services
const mockServices = {
  canSwitch: vi.fn(),
  switchUser: vi.fn()
}

vi.mock('@/services', () => {
  return {
    default: vi.fn().mockImplementation(() => mockServices)
  }
})

describe('Switch User Module', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    Object.values(mockServices).forEach(mock => {
      if (vi.isMockFunction(mock)) {
        mock.mockClear()
      }
    })
  })

  describe('SwitchUser Class', () => {
    describe('constructor', () => {
      it('应该在prod环境中抛出错误', () => {
        expect(() => {
          new SwitchUser('prod', 'test-client', 'test-domain')
        }).toThrow('env is invalid')
      })

      it('应该在test环境中正常创建实例', () => {
        expect(() => {
          new SwitchUser('test', 'test-client', 'test-domain')
        }).not.toThrow()
      })

      it('应该在uat环境中正常创建实例', () => {
        expect(() => {
          new SwitchUser('uat', 'test-client', 'test-domain')
        }).not.toThrow()
      })

      it('应该在缺少参数时抛出错误', () => {
        expect(() => {
          new SwitchUser('' as Env, 'test-client', 'test-domain')
        }).toThrow('env is required')

        expect(() => {
          new SwitchUser('test', '', 'test-domain')
        }).toThrow('clientId is required')

        expect(() => {
          new SwitchUser('test', 'test-client', '')
        }).toThrow('domain is required')
      })
    })

    describe('canSwitchUser', () => {
      it('应该在test环境中工作', async () => {
        mockServices.canSwitch.mockResolvedValue({ code: 0, data: { can_switch: true } })

        const switchUser = new SwitchUser('test', 'test-client', 'test-domain')
        const result = await switchUser.canSwitchUser()

        expect(mockServices.canSwitch).toHaveBeenCalledWith('test-domain')
        expect(result).toBe(true)
      })

      it('应该在uat环境中工作', async () => {
        mockServices.canSwitch.mockResolvedValue({ code: 0, data: { can_switch: false } })

        const switchUser = new SwitchUser('uat', 'test-client', 'test-domain')
        const result = await switchUser.canSwitchUser()

        expect(mockServices.canSwitch).toHaveBeenCalledWith('test-domain')
        expect(result).toBe(false)
      })

      it('应该处理服务错误', async () => {
        mockServices.canSwitch.mockResolvedValue({ code: -1, message: 'Service error' })

        const switchUser = new SwitchUser('test', 'test-client', 'test-domain')
        const result = await switchUser.canSwitchUser()

        expect(result).toBe(false)
      })
    })

    describe('switchUser', () => {
      it('应该在test环境中执行用户切换', async () => {
        mockServices.switchUser.mockResolvedValue({
          code: 0,
          message: 'success'
        })

        const switchUser = new SwitchUser('test', 'test-client', 'test-domain')
        const result = await switchUser.switchUser()

        expect(mockServices.switchUser).toHaveBeenCalledWith('test-domain')
        expect(result).toEqual({
          result: true,
          message: 'success'
        })
      })

      it('应该在uat环境中执行用户切换', async () => {
        mockServices.switchUser.mockResolvedValue({
          code: 0,
          message: 'success'
        })

        const switchUser = new SwitchUser('uat', 'test-client', 'test-domain')
        const result = await switchUser.switchUser()

        expect(mockServices.switchUser).toHaveBeenCalledWith('test-domain')
        expect(result).toEqual({
          result: true,
          message: 'success'
        })
      })

      it('应该处理切换失败', async () => {
        mockServices.switchUser.mockResolvedValue({
          code: -1,
          message: 'Switch failed'
        })

        const switchUser = new SwitchUser('test', 'test-client', 'test-domain')
        const result = await switchUser.switchUser()

        expect(result).toEqual({
          result: false,
          message: 'Switch failed'
        })
      })
    })
  })

  describe('环境验证', () => {
    it('应该接受test环境', () => {
      expect(() => {
        new SwitchUser('test', 'test-client', 'test-domain')
      }).not.toThrow()
    })

    it('应该接受uat环境', () => {
      expect(() => {
        new SwitchUser('uat', 'test-client', 'test-domain')
      }).not.toThrow()
    })

    it('应该拒绝prod环境', () => {
      expect(() => {
        new SwitchUser('prod', 'test-client', 'test-domain')
      }).toThrow('env is invalid')
    })

    it('应该拒绝pp环境', () => {
      expect(() => {
        new SwitchUser('pp', 'test-client', 'test-domain')
      }).toThrow('env is invalid')
    })
  })
})