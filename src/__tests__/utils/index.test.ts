import {beforeEach, describe, expect, it, vi} from 'vitest'
import {
  createIframe,
  deepEqual,
  getBaseUrl,
  getIsWaveEnv,
  getRedirectUri,
  getShim,
  getTLD,
  getUrl,
  getWebsiteDomain
} from '@/utils'
import type {IInitConfig} from '@/types'

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'https://example.com/test',
    hostname: 'example.com',
    origin: 'https://example.com'
  },
  writable: true
})

// Mock Cookie
vi.mock('@/utils/cookie', () => ({
  default: vi.fn().mockImplementation(() => ({
    get: vi.fn().mockReturnValue('test-cookie-value')
  }))
}))

describe('Utils Module', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock navigator.userAgent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      writable: true
    })


    // Clean up any existing iframe
    const existingIframe = document.getElementById('iam-login-next')
    if (existingIframe) {
      existingIframe.remove()
    }
  })

  describe('getRedirectUri', () => {
    it('应该返回URL编码的当前页面URL', () => {
      const result = getRedirectUri()
      expect(result).toBe('https%3A%2F%2Fexample.com%2Ftest')
    })
  })

  describe('getTLD', () => {
    it('应该返回顶级域名', () => {
      const result = getTLD()
      expect(result).toBe('example.com')
    })

    it('应该为localhost返回默认域名', () => {
      Object.defineProperty(window, 'location', {
        value: {hostname: 'localhost'},
        writable: true
      })

      const result = getTLD()
      expect(result).toBe('mihoyo.com')
    })
  })

  describe('getBaseUrl', () => {
    it('应该为test环境返回正确的URL', () => {
      const result = getBaseUrl('test')
      expect(result).toBe('https://api-test.agw.mihoyo.com')
    })

    it('应该为prod环境返回正确的URL', () => {
      const result = getBaseUrl('prod')
      expect(result).toBe('https://api.agw.mihoyo.com')
    })

    it('应该为pp环境返回正确的URL', () => {
      const result = getBaseUrl('pp')
      expect(result).toBe('https://api-pre.agw.mihoyo.com')
    })
  })

  describe('getWebsiteDomain', () => {
    it('应该为test环境返回正确的URL', () => {
      const result = getWebsiteDomain('test')
      expect(result).toBe('https://iamtest.mihoyo.com/website')
    })

    it('应该为prod环境返回正确的URL', () => {
      const result = getWebsiteDomain('prod')
      expect(result).toBe('https://iam.mihoyo.com/website')
    })

    it('应该为pp环境返回正确的URL', () => {
      const result = getWebsiteDomain('pp')
      expect(result).toBe('https://iampp01.app.mihoyo.com/website')
    })
  })

  describe('getUrl', () => {
    it('应该生成包含所有必要参数的URL', () => {
      const config: IInitConfig = {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN'
      }

      const result = getUrl('https://example.com', '/login', config)

      expect(result.toString()).toContain('clientId=test-client')
      expect(result.toString()).toContain('lang=zh-CN')
      expect(result.toString()).toContain('redirectUri=https%253A%252F%252Fexample.com%252Ftest')
      expect(result.toString()).toContain('callbackType=h5')
    })

    it('应该过滤掉不需要的参数', () => {
      const config: IInitConfig = {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN',
        handleLang: vi.fn(),
        messageSlot: 'test-slot' as any,
        getOTPToken: vi.fn(),
        handleMfaClose: vi.fn()
      }

      const result = getUrl('https://example.com', '/login', config)

      expect(result.toString()).not.toContain('handleLang')
      expect(result.toString()).not.toContain('messageSlot')
      expect(result.toString()).not.toContain('getOTPToken')
      expect(result.toString()).not.toContain('handleMfaClose')
    })
  })

  describe('createIframe', () => {
    it('应该创建iframe并返回Promise<Window>', async () => {
      const rootElement = document.createElement('div')
      document.body.appendChild(rootElement)

      // Mock iframe onload
      const originalCreateElement = document.createElement
      vi.spyOn(document, 'createElement').mockImplementation((tagName) => {
        const element = originalCreateElement.call(document, tagName)
        if (tagName === 'iframe') {
          // Mock contentWindow
          Object.defineProperty(element, 'contentWindow', {
            value: window,
            writable: true
          })
          // Trigger onload immediately
          setTimeout(() => {
            if (element.onload) {
              element.onload({} as Event)
            }
          }, 0)
        }
        return element
      })

      const promise = createIframe('https://example.com', rootElement)
      const result = await promise

      expect(result).toBe(window)

      const iframe = document.getElementById('iam-login-next') as HTMLIFrameElement
      expect(iframe).toBeTruthy()
      expect(iframe.src).toBe('https://example.com/')

      // Cleanup
      document.body.removeChild(rootElement)
      vi.restoreAllMocks()
    })
  })

  describe('getShim', () => {
    it('应该为test环境返回cookie值', () => {
      const result = getShim('test')
      expect(result).toBe('test-cookie-value')
    })

    it('应该为prod环境返回cookie值', () => {
      const result = getShim('prod')
      expect(result).toBe('test-cookie-value')
    })
  })

  describe('getIsWaveEnv', () => {
    it('应该在Wave环境中返回true', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) hoyowave/1.0',
        writable: true
      })

      const result = getIsWaveEnv()
      expect(result).toBe(true)
    })

    it('应该在非Wave环境中返回false', () => {
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        writable: true
      })

      const result = getIsWaveEnv()
      expect(result).toBe(false)
    })
  })

  describe('deepEqual', () => {
    it('应该对相同对象返回true', () => {
      const config1 = {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN',
        extraParams: {key: 'value'}
      } as IInitConfig

      const config2 = {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN',
        extraParams: {key: 'value'}
      } as IInitConfig

      expect(deepEqual(config1, config2)).toBe(true)
    })

    it('应该对不同对象返回false', () => {
      const config1 = {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN'
      } as IInitConfig

      const config2 = {
        clientId: 'different-client',
        env: 'test',
        lang: 'zh-CN'
      } as IInitConfig

      expect(deepEqual(config1, config2)).toBe(false)
    })

    it('应该处理函数比较', () => {
      const func = () => 'test'

      const config1: IInitConfig = {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN',
        handleLang: func
      }

      const config2: IInitConfig = {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN',
        handleLang: func
      }

      expect(deepEqual(config1, config2)).toBe(true)
    })
  })
})
