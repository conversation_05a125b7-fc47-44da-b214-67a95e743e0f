import {beforeEach, describe, expect, it, vi} from 'vitest'
import Logout from '@/logout'
import {Env, Lang} from "@/types";
import EnvMap from "@/utils/env";
import {MIHOYO_DOMAIN} from "@/utils/constant";
import Services from '@/services'

// Mock Services 类
vi.mock('@/services')

describe('Logout Module', () => {
  const env: Env = 'prod'
  const lang: Lang = 'zh-CN'
  const href = 'https://sso.mihoyo.com'
  const hostname = MIHOYO_DOMAIN

  beforeEach(() => {
    vi.clearAllMocks()

    // 设置 Services mock
    const mockLogout = vi.fn().mockResolvedValue([null, null])
    vi.mocked(Services).mockImplementation(() => ({
      logout: mockLogout
    } as any))

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        href,
        hostname
      },
      writable: true
    })
  })

  describe('参数校验', () => {
    it('env 校验', () => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      expect(() => new Logout()).toThrowError(new Error('env is required'))
    })
  })

  describe('logoutBySSO', () => {
    it('应该使用SSO方式登出', () => {
      const instance = new Logout(env, lang)
      instance.logoutBySSO()
      const url = new URL(window.location.href)
      expect(url.origin + url.pathname).toEqual(EnvMap[env].IAM_WEBSITE_CN + '/logout')
      expect(url.searchParams.get('lang')).toEqual(lang)
      expect(url.searchParams.get('postLogoutRedirectUri')).toEqual(href)
    })
  })

  describe('logoutByApi', () => {
    it('应该使用API方式登出', async () => {
      const instance = new Logout(env, lang)
      const result = await instance.logoutByApi()
      expect(result).toEqual([null, null])
    })

    it('应该处理登出失败', async () => {
      // 重新设置 mock 返回失败结果
      const mockLogout = vi.fn().mockResolvedValue([{code: -1, msg: 'logout failed'}, {code: -1, msg: 'logout failed'}])
      vi.mocked(Services).mockImplementation(() => ({
        logout: mockLogout
      } as any))

      const instance = new Logout(env, lang)
      const result = await instance.logoutByApi()

      expect(result).toEqual([{code: -1, msg: 'logout failed'}, {code: -1, msg: 'logout failed'}])
    })

    it('应该处理网络错误', async () => {
      // 重新设置 mock 抛出错误
      const mockLogout = vi.fn().mockRejectedValue(new Error('Network error'))
      vi.mocked(Services).mockImplementation(() => ({
        logout: mockLogout
      } as any))

      const instance = new Logout(env, lang)

      await expect(instance.logoutByApi()).rejects.toThrow('Network error')
    })
  })
})