import {beforeEach, describe, expect, it, vi} from 'vitest'
import Services from '@/services'
import type {Ctx} from '@/types'

// Mock fetch
global.fetch = vi.fn()

describe('Services', () => {
  let services: Services
  let mockCtx: Ctx

  beforeEach(() => {
    vi.clearAllMocks()

    mockCtx = {
      isWaveEnv: false,
      isLogin: false,
      isRender: false,
      errObj: {},
      config: {
        clientId: 'test-client',
        env: 'test',
        lang: 'zh-CN'
      }
    }

    services = new Services('test', 'test-client', 'zh-CN', mockCtx)

    // Mock successful fetch response
    vi.mocked(fetch).mockResolvedValue({
      ok: true,
      status: 200,
      statusText: 'OK',
      json: () => Promise.resolve({code: 0, data: {result: 'success'}})
    } as unknown as Response)
  })

  describe('constructor', () => {
    it('应该正确初始化服务实例', () => {
      expect(services).toBeInstanceOf(Services)
    })
  })

  describe('sessionCheck', () => {
    it('应该成功检查会话状态', async () => {
      const result = await services.sessionCheck()

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/session/check'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({}),
          credentials: 'include'
        })
      )
      expect(result).toEqual({code: 0, data: {result: 'success'}})
    })

    it('应该处理网络错误', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Network error'))

      const result = await services.sessionCheck()
      expect(result).toEqual({
        url: expect.any(String),
        code: -9999,
        params: {},
        message: 'Error: Network error'
      })
    })
  })

  describe('shimGetCorsTicket', () => {
    it('应该成功获取CORS票据', async () => {
      const result = await services.shimGetCorsTicket()

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/shim/get_cors_ticket'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: expect.stringContaining('clientId'),
          credentials: 'include'
        })
      )
      expect(result).toEqual({code: 0, data: {result: 'success'}})
    })
  })

  describe('shimTicketLogin', () => {
    it('应该成功执行票据登录', async () => {
      const ticket = 'test-ticket'
      const result = await services.shimTicketLogin(ticket)

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/shim/ticket_login'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({ticket}),
          credentials: 'include'
        })
      )
      expect(result).toEqual({code: 0, data: {result: 'success'}})
    })
  })

  describe('waveSilenceLogin', () => {
    it('应该成功执行Wave静默登录', async () => {
      const otp = 'test-otp'
      const result = await services.waveSilenceLogin(otp)

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/wave/silence_login'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({
            verify_code: otp,
            otp_scene: 'InitialLogin',
            is_from_wave: true
          }),
          credentials: 'include'
        })
      )
      expect(result).toEqual({code: 0, data: {result: 'success'}})
    })
  })

  describe('syncSessionToIam', () => {
    it('应该成功同步会话到IAM', async () => {
      const ticket = 'test-ticket'
      const clientId = 'test-client-id'
      const result = await services.syncSessionToIam(ticket, clientId)

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/shim/moat_silent_login'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({ticket, clientId}),
          credentials: 'include'
        })
      )
      expect(result).toEqual({code: 0, data: {result: 'success'}})
    })
  })

  describe('canSwitch', () => {
    it('应该成功检查是否可以切换用户', async () => {
      const domain = 'test-domain'
      const result = await services.canSwitch(domain)

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/switch/can_switch'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({domain}),
          credentials: 'include'
        })
      )
      expect(result).toEqual({code: 0, data: {result: 'success'}})
    })
  })

  describe('switchUser', () => {
    it('应该成功切换用户', async () => {
      const domain = 'test-domain'
      const result = await services.switchUser(domain)

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/switch/user'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({domain}),
          credentials: 'include'
        })
      )
      expect(result).toEqual({code: 0, data: {result: 'success'}})
    })
  })

  describe('错误处理', () => {
    it('应该处理HTTP错误状态', async () => {
      vi.mocked(fetch).mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as unknown as Response)

      const result = await services.sessionCheck()
      expect(result).toEqual({
        url: expect.any(String),
        code: -9999,
        params: {},
        message: 'Error: 请求错误: 500Internal Server Error'
      })
    })

    it('应该处理JSON解析错误', async () => {
      vi.mocked(fetch).mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.reject(new Error('Invalid JSON'))
      } as unknown as Response)

      const result = await services.sessionCheck()
      expect(result).toEqual({
        url: expect.any(String),
        code: -9999,
        params: {},
        message: 'Error: Invalid JSON'
      })
    })

    it('应该处理超时', async () => {
      // Mock a very slow response
      vi.mocked(fetch).mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({code: 0})
        } as unknown as Response), 15000))
      )

      const result = await services.sessionCheck()
      expect(result).toEqual({
        url: expect.any(String),
        code: -9999,
        params: {},
        message: '10000ms timeout'
      })
    }, 12000)
  })
})
