import { vi } from 'vitest'

// 全局测试设置
beforeEach(() => {
  // 重置所有mocks
  vi.clearAllMocks()
  
  // 重置DOM
  document.body.innerHTML = ''
  
  // 重置window对象
  delete (window as any).hoyowaveJsApi
  
  // Mock console.log, console.warn, console.error 以避免测试输出干扰
  vi.spyOn(console, 'log').mockImplementation(() => {})
  vi.spyOn(console, 'warn').mockImplementation(() => {})
  vi.spyOn(console, 'error').mockImplementation(() => {})
})

afterEach(() => {
  // 恢复所有mocks
  vi.restoreAllMocks()
})

// 全局fetch mock
global.fetch = vi.fn()

// 全局window对象mock
Object.defineProperty(window, 'location', {
  value: {
    href: 'https://example.com',
    origin: 'https://example.com',
    hostname: 'example.com',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true
})

// MessageEvent polyfill for tests
if (!global.MessageEvent) {
  global.MessageEvent = class MessageEvent extends Event {
    data: any
    origin: string
    source: any

    constructor(type: string, eventInit?: { data?: any; origin?: string; source?: any }) {
      super(type)
      this.data = eventInit?.data
      this.origin = eventInit?.origin || ''
      this.source = eventInit?.source
    }
  } as any
}