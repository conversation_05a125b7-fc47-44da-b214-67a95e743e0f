import { ErrorCb, <PERSON>amObj, <PERSON><PERSON><PERSON><PERSON>, SuccessCb } from '../types';

class Callback {
  private readonly defaultObj: IamObj;

  constructor(private callback: (iamObj: IamObj) => void) {
    if (!callback) {
      throw new Error('callback is required');
    }
    this.defaultObj = {
      onSuccess: () => {},
      onError: () => {},
      onFail: () => {},
    };
  }

  success() {
    const obj = {
      ...this.defaultObj,
      onSuccess: (cb: SuccessCb) => {
        cb?.();
      },
    };
    this.callback(obj);
  }

  error(info: IError) {
    const obj = {
      ...this.defaultObj,
      onError: (cb: ErrorCb) => {
        cb?.(info);
      },
    };
    this.callback(obj);
  }

  fail(info: string) {
    const obj = {
      ...this.defaultObj,
      onFail: (cb: (failInfo: string) => void) => {
        cb?.(info);
      },
    } as unknown as <PERSON>amObj;
    this.callback(obj);
  }
}

export default Callback;
