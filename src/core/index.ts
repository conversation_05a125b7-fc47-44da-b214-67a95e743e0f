import {Ctx, IamObj, IInitConfig} from '@/types';
import Callback from '@/core/callback';
import {destroy, render} from './iframe';
import Singleton from '@/core/singleton';
import listeners from './listeners';
import {unsubscribeAll} from "../message";
import {getIsWaveEnv, getMoatClientId} from "@/utils";
import Services from "../services";
import {WAVE_SILENCE_WORK_CODE} from "@/utils/constant";
import {subscribeSuccessCallback} from '@/utils/action';

class Main extends Singleton {
  private ctx: Ctx

  constructor() {
    super();
    this.ctx = {
      isWaveEnv: getIsWaveEnv(),
      isLogin: false,
      isRender: false,
      errObj: {},
    }
  }

  clean() {
    destroy();
    unsubscribeAll();
    this.ctx.isLogin = false
    this.isExecuting = false;
    this.ctx.isRender = false;
  }

  // sessionCheck
  async sessionCheck(ctx: Ctx) {
    if (this.ctx.isWaveEnv) {
      return ctx
    }
    if (ctx.isLogin) {
      return ctx
    }
    const {env, clientId, lang} = ctx.config;
    const services = new Services(env, clientId, lang, ctx);
    const data = await services.sessionCheck();
    if (data?.code === 0) {
      ctx.isLogin = true;
      return ctx
    }
    if (data?.code === -5) {
      ctx.config.scene = 'second_mfa';
      ctx.isLogin = false;
      return ctx
    }
    ctx.isLogin = false;
    return ctx
  }

  // 判断是否需要同步 session 到 moat
  async SyncSessionToMoat(ctx: Ctx) {
    if (ctx.isWaveEnv) {
      return ctx
    }
    if (ctx.isLogin) {
      return ctx
    }
    if (ctx.config.scene === 'second_mfa') {
      return ctx
    }
    const {env, clientId, lang} = ctx.config;
    let skipShim: boolean;
    const services = new Services(env, clientId, lang, ctx);
    // 判断是否需要 skipShim, 判断标准为 third_party_auth_method_configs 如果存在则跳过
    const pageConfigResponse = await services.getPageConfig()
    if (pageConfigResponse?.code === 0) {
      const pageConfig = pageConfigResponse?.data;
      const {third_party_auth_method_configs} = pageConfig
      skipShim = third_party_auth_method_configs && third_party_auth_method_configs.length > 0
    } else {
      skipShim = false
    }
    if (!skipShim) {
      const data = await services.shimGetCorsTicket();
      if (data?.code === 0) {
        const result = await services.shimTicketLogin(data?.data?.ticket);
        if (result?.code === 0) {
          ctx.isLogin = true;
          return ctx;
        }
        if (result?.code === -5) {
          ctx.config.scene = 'second_mfa';
          ctx.isLogin = false;
          return ctx
        }
      }
    }
    ctx.isLogin = false;
    return ctx
  }

  // wave 免登
  async waveSilenceLogin(ctx: Ctx) {
    if (ctx.isLogin) {
      return ctx
    }
    if (ctx.config.scene === 'second_mfa') {
      return ctx
    }
    if (ctx.isWaveEnv) {
      let _zt_ticket: string
      const {env, clientId, lang, getOTPToken = null, hoyowaveJsApi = null, waveOtp = ''} = ctx.config;
      const services = new Services(env, clientId, lang, ctx);
      // getOTPToken 优先使用 getOTPToken 函数，如果为空则使用 hoyowaveJsApi 的 getOTPToken 函数
      if (hoyowaveJsApi?.getZTTicket) {
        try {
          const data = await services.getWaveJsTicketAndAppId();
          if (data?.code === 0) {
            const {ticket, app_id: appId} = data.data
            await hoyowaveJsApi?.config({appId, ticket, jsApiList: ['getZTTicket']});
            // 获取zt_ticket
            const {errCode, errMsg, zt_ticket} = await hoyowaveJsApi.getZTTicket()
            if (errCode !== 0) {
              // 不阻塞后续逻辑，获取失败不影响后续登录,因为这是非关键错误，不需要上报到 Sentry
              console.error('getZTTicket faild:' + `errCode: ${errCode}, errMsg: ${errMsg}`)
            }
            _zt_ticket = zt_ticket
          } else {
            console.error('getWaveJsTicketAndAppId faild:' + `code: ${data.code}, message: ${data.message}`)
          }
        } catch (error) {
          // 捕获getZTTicket异常，确保不阻塞后续登录流程
          console.error('getZTTicket执行过程中发生异常:', error)
        }
      } else if (!_zt_ticket) {
        try {
          // 刷新ztTokenCookie
          const {errCode, errMsg} = await hoyowaveJsApi.refreshZTTokenCookie()
          if (errCode !== 0) {
            // 不阻塞后续逻辑，刷新失败不影响后续登录,因为这是非关键错误，不需要上报到 Sentry
            console.error('refreshZTTokenCookie faild:' + `errCode: ${errCode}, errMsg: ${errMsg}`)
          }
        } catch (error) {
          // 捕获refreshZTTokenCookie异常，确保不阻塞后续登录流程
          console.error('refreshZTTokenCookie执行过程中发生异常:', error)
        }
      }
      const _getOTPToken = getOTPToken || hoyowaveJsApi?.getOTPToken;
      if (!_getOTPToken && !waveOtp) {
        ctx.isLogin = false;
        return ctx
      }
      let _waveOpt = waveOtp
      if (!_waveOpt) {
        const {otp} = await _getOTPToken()
        if (!otp) {
          ctx.isLogin = false;
          return ctx
        }
        _waveOpt = otp
      }
      const data = await services.waveSilenceLogin(_waveOpt, _zt_ticket);
      const code = data?.code
      if (code === 0) {
        const ticket = data?.data?.ticket
        const clientId = getMoatClientId(env)
        if (ticket) {
          await services.syncSessionToIam(ticket, clientId).then().catch().finally(() => ctx.isLogin = true)
        }
        ctx.isLogin = true
      }
      if (code === WAVE_SILENCE_WORK_CODE) {
        ctx.isLogin = false
        ctx.extraProps = {
          ...ctx.extraProps,
          workCode: WAVE_SILENCE_WORK_CODE,
          messageSlot: ctx.config.messageSlot
        }
      }
      return ctx
    }
    return ctx
  }

  // 渲染 iframe
  initRender(ctx = this.ctx) {
    if (ctx.isRender) {
      return Promise.resolve(ctx)
    }
    if (ctx.isLogin) {
      return Promise.resolve(ctx)
    }
    ctx.isRender = true
    return render(ctx)
    .then(listeners)
    .then(() => ctx)
  }

  // 认证成功
  success(ctx = this.ctx) {
    const cb = () => {

      this.clean()
      ctx.callback.success();
    }
    // 针对于 同步成功 或者 wave 免登的场景下
    if (ctx.isLogin) {
      cb()
    }
    subscribeSuccessCallback(() => {
      cb()
    })
    // ACTION.SUCCESS_CALLBACK
    // window.addEventListener('message', (e) => {
    //   if (e?.data?.eventName === ACTION.SUCCESS_CALLBACK) {
    //     cb()
    //   }
    // });
  }

  // 检测入参
  checkArgs() {
    const {config} = this.ctx
    if (!config) {
      throw new Error('config is required');
    }
    if (!config.clientId) {
      throw new Error('clientId is required');
    }
    if (!config.env) {
      throw new Error('env is required');
    }

    return true;
  }

  // render 前置逻辑，兼容不渲染登录页的场景
  checkRender(ctx = this.ctx) {
    const {showPage = true} = ctx.config;
    if (!showPage) {
      ctx.isRender = false;
      ctx.callback.fail('FAIL_LOGIN');
      return Promise.reject('FAIL_LOGIN');
    }
    return ctx
  }

  // 入口函数
  initIamLogin(config: IInitConfig, callback: (iamObj: IamObj) => void) {
    // 检查当前是否已有实例，保证单例模式
    if (this.isExecuting) {
      console.warn('Already executing initIamLogin, please wait.', config);
      return;
    }
    console.log('initIamLogin config', config)
    this.isExecuting = true;
    const _callback = new Callback(callback);
    this.ctx = {
      ...this.ctx,
      config,
      callback: _callback,
      clean: this.clean.bind(this)
    }

    // 先检查参数是否合法
    this.checkArgs();
    this.waveSilenceLogin(this.ctx)
    // .then(this.sessionCheck.bind(this))
    .then(this.SyncSessionToMoat.bind(this))
    .then(this.checkRender.bind(this))
    .then(this.initRender.bind(this))
    .then(this.success.bind(this))
    .catch((err) => {
      this.isExecuting = false;
      this.ctx.isRender = false;
      this.ctx.isLogin = false;
      // 除了显式明确不渲染登录页，其他情况下异常都会渲染登录页
      if (this.ctx.config.showPage) {
        this.initRender(this.ctx).then(this.success.bind(this));
        _callback.error(err);
      }
    })
  }
}

const instance = new Main();

export {
  instance,
  Main
}

export default instance.initIamLogin.bind(instance);
