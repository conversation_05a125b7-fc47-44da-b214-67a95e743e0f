import {
  changeLocation,
  handleDestroy,
  handleLang,
  openLocation,
  postLocation,
  reloadLocation,
  sendExtraProps,
  sendRequestError
} from '@/utils/action';
import {Ctx} from "@/types";
import {WAVE_SILENCE_WORK_CODE} from "@/utils/constant";

// 传递 top window 的 location.href
const postLocationListener = () => {
  postLocation(window.location.href);
  return Promise.resolve();
};

// 接收需要跳转的 url， 在 top window 中跳转
const changeLocationListener = () => {
  changeLocation();
  return Promise.resolve();
};

// 接收需要打开的 url， 在 top window 中打开
const openLocationListener = () => {
  openLocation();
  return Promise.resolve();
};

// 接收需要刷新的指令， 在 top window 中刷新
const reloadLocationListener = () => {
  reloadLocation();
  return Promise.resolve();
};

// 接收请求错误信息， 在 iframe 中上报
const sendRequestErrorListener = (ctx: Ctx) => {
  const {code, message, url, params} = ctx.errObj;
  if (code !== WAVE_SILENCE_WORK_CODE) {
    sendRequestError(code, message, url, params)
  }
  return Promise.resolve();
}

// 发送消息插槽, 在 iframe 中处理
const sendExtraPropsListener = (ctx: Ctx) => {
  const {extraProps} = ctx;
  sendExtraProps(extraProps)
  return Promise.resolve();
}

const handleLangListener = (ctx: Ctx) => {
  const {config: {handleLang: _handleLang}} = ctx
  if (_handleLang) {
    handleLang(_handleLang);
  }
  return Promise.resolve();
}

const sendDestroyListener = (ctx: Ctx) => {
  const {
    config: {
      handleMfaClose = () => {
      }
    },
    clean
  } = ctx
  handleDestroy(handleMfaClose, clean);
  return Promise.resolve();
}


export default (ctx: Ctx) => {
  return Promise.allSettled([postLocationListener(), changeLocationListener(), openLocationListener(), reloadLocationListener(), sendRequestErrorListener(ctx), sendExtraPropsListener(ctx), handleLangListener(ctx), sendDestroyListener(ctx)]);
};
