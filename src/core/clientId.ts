/**
 * the func to manage clientId
 * @param {string} clientId - 可以初始化传入 clientId，那 instance 就会一直持有这个值, 且只对当前 instance 生效。
 */
import {APP_TYPE} from "@/types";

class ClientIdManage {
  private readonly appType: APP_TYPE;

  constructor(public clientId = '') {
    this.appType = this.detectAppType();
  }

  /**
   * Detects the type of the current application based on the environment.
   *
   * @returns {APP_TYPE} The type of the current application.
   */
  private detectAppType = (): APP_TYPE => {
    // 判断是否是 SSR（可以根据你的实际情况进行判断，比如检查某些服务器端渲染特有的全局变量）
    if (typeof window === "undefined") {
      return APP_TYPE.SSR;
    }

    // 判断是否是 Iframe
    if (window.self !== window.top) {
      return APP_TYPE.IFRAME;
    }

    // 判断是否是 Qiankun 子应用
    if (window.__POWERED_BY_QIANKUN__) {
      return APP_TYPE.QIANKUN_SUB_APP;
    }

    // 默认返回 OTHER
    return APP_TYPE.OTHER;
  }

  /**
   * Get the client ID from the query parameters.
   *
   * @returns {string} The client ID.
   */

  private getClientIdByQueryParameters = (): string => {
    const _href = new URL(location.href)
    // 兼容未来可能 clientId 的 query params 驼峰或者 下划线
    const clientId = _href.searchParams.get('clientId') || _href.searchParams.get('client_id')
    if (clientId) {
      return clientId
    }
    return this.getClientIdByMetaData()
  }

  /**
   * Get the client ID from the metadata.
   *
   * @returns {string} The client ID.
   */

  private getClientIdByMetaData = (): string => {
    const nodeList = document.querySelectorAll('meta[name=clientId]')
    const len = nodeList.length
    if (len > 0) {
      return nodeList[0].getAttribute('content') || ''
    }
    return this.clientId
  }

  /**
   * Get the client ID.
   *
   * @returns {string} The client ID.
   */
  public getClientId = (): string => {
    if (this.clientId) {
      return this.clientId;
    }
    if (this.appType === APP_TYPE.IFRAME) {
      return this.getClientIdByQueryParameters()
    }
    return this.getClientIdByMetaData()
  }

}


export default ClientIdManage.bind(ClientIdManage);


