import {Ctx} from '@/types';
import {createIframe, getUrl, getWebsiteDomain} from '@/utils';
import {IFRAME_LOAD_ERROR} from '@/utils/model';
import {IFRAME_ID, SCENE_TO_PATH_SCHEME} from '@/utils/constant';

const destroy = () => {
  const ele = document.getElementById(IFRAME_ID);
  if (!ele) {
    return null;
  }
  try {
    if (ele.parentNode) {
      ele.parentNode.removeChild(ele);
    }
  } catch (error) {
    console.error('Failed to remove iframe element:', error);
  }
};

const render = async (ctx: Ctx) => {
  const {config} = ctx
  const path = SCENE_TO_PATH_SCHEME[config.scene];
  const domain = getWebsiteDomain(config.env);
  const url = getUrl(domain, path, config);
  const rootElement = config?.rootElement || document.body;
  return new Promise((resolve, reject) => {
    createIframe(url.toString(), rootElement, config.wrapperStyle)
    .then((iframeWindow) => {
      ctx.iframeWindow = iframeWindow;
      // 与 iframe 通讯成功
      resolve(ctx);
      // requestLoadMessage(iframeWindow, origin).then((res) => {
      //   console.log('在 sdk中 收到了消息')
      //   // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //   // @ts-expect-error
      //   if (res.status === 'ok') {
      //     ctx.iframeWindow = iframeWindow;
      //     // 与 iframe 通讯成功
      //     resolve(ctx);
      //     return;
      //   }
      // });
    })
    .catch(() => {
      reject(IFRAME_LOAD_ERROR);
    });
  });
};

export {render, destroy};
