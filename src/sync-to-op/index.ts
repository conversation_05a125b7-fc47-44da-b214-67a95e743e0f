import {Env, OpType} from "@/types";
import Services from "@/services";
import {getOpConfig} from "@/utils/opConfig";

class SyncToOp {
  protected services: Services;

  constructor(protected readonly env: Env, private clientId: string, protected readonly type = OpType.Op, private appKey?: string,) {
    if (!this.env) {
      throw new Error('env is required');
    }
    this.services = new Services(this.env, this.clientId, 'zh-CN', {}, {
      headers: {
        'x-rpc-app_key': this.appKey
      }
    })
  }

  private async getAuthTicket() {

    return await this.services.getJsTicket()
  }

  private async ticketToToken(ticket: string) {
    const config = getOpConfig(this.type, this.env)
    const url = config.sysLoginApi
    if (url) {
      return await this.services.opTicketToToken(url, ticket)
    }
    return Promise.reject('sysLoginApi is required')

  }

  public async sync() {
    const data = await this.getAuthTicket()
    if (data?.code === 0) {
      const ticket = data?.data?.ticket
      const result = await this.ticketToToken(ticket)
      return result?.code === 0;

    }
    return false
  }
}


export default SyncToOp


// const instance = new window.iamNext.SyncToOp('test', '8b2a8d72e24b36ed', 'op')
// instance.sync().then(res => {
//   console.log('res', res)
// })
