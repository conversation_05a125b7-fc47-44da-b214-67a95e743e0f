import type {Response} from '@/types';

const request = async <T = any>(url: string, params: Record<string, any>, options: Omit<RequestInit, 'body'>): Promise<Response<T>> => {
  let timeoutId: NodeJS.Timeout;
  const timeoutPromise = new Promise<never>((_, reject) => {
    timeoutId = setTimeout(() => {
      reject({
        url,
        code: -9999,
        params,
        message: '10000ms timeout',
      });
    }, 10000);
  });

  const fetchPromise = fetch(url, {
    body: JSON.stringify(params),
    ...options,
    credentials: 'include',
  })
  .then(async res => {
    if (res.status >= 200 && res.status < 300) {
      return res
    } else {
      throw new Error(`请求错误: ${res.status}` + res.statusText)
    }
  })
  .then((res) => res.json())
  .catch((e: any) => {
    return Promise.reject({
      url,
      code: -9999,
      params,
      message: e?.toString(),
    });
  });

  return Promise.race([fetchPromise, timeoutPromise])
  .finally(() => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
  });
};

export default request;



