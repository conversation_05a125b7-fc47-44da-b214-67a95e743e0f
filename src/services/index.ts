import request from './request';
import {getAuthDomain, getBaseUrl, getMoatClientId} from '@/utils';
import {CanSwitchResult, Ctx, Env, IV3LoginPageConfig, Lang, Response, UserInfo} from '../types';
import {
  authnV3LogOutApi,
  canSwitchApi,
  checkLogin<PERSON>pi,
  getCorsTicketApi,
  logOutApi,
  moatSilentLoginApi,
  pageConfigApi,
  sessionCheck,
  shimGetCorsTicket,
  shimTicketLogin,
  switchUserApi,
  waveLogin,
} from './routes';
import EnvMap from '../utils/env';

class Services {
  private readonly request: <T = any>(url: string, params: Record<string, any>) => Promise<Response<T>>;
  private readonly authRequest: <T = any>(url: string, params: Record<string, any>) => Promise<Response<T>>;
  private readonly otherRequest: <T = any>(url: string, params: Record<string, any>) => Promise<Response<T>>;
  private workCodes: number[];

  constructor(
    private env: Env,
    private clientId: string,
    private lang: Lang,
    private ctx: Ctx,
    private config?: RequestInit,
  ) {
    this.request = this.createRequest();
    this.authRequest = this.createAuthRequest();
    this.otherRequest = this.createOtherRequest();
    this.workCodes = [-3, -5, 0]
  }

  // agw.mihoyo.com
  createRequest(headers?: Record<string, any>, options?: Omit<RequestInit, 'body' | 'headers'>) {
    const baseUrl = getBaseUrl(this.env);
    const _headers = new Headers();
    if (headers) {
      Object.keys(headers).forEach((key) => {
        _headers.append(key, headers[key]);
      });
    }
    const _options = {
      ...this.config,
      ...options,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Mi-Clientid': this.clientId,
        'accept-language': this.lang,
        ...headers,
      },
    };
    return async (url: string, params: Record<string, any>) => {
      return request(`${baseUrl}${EnvMap[this.env].IAM_SRV_PREFIX}${url}`, params, _options)
      .then((res) => {
        if (!this.workCodes.includes(res?.code)) {
          if (this.ctx.errObj) {
            this.ctx.errObj.code = res.code
            this.ctx.errObj.message = res.message
            this.ctx.errObj.url = url
            this.ctx.errObj.params = params
          }
        }
        return res
      })
      .catch(e => {
        this.ctx.errObj = e
        return e
      })
    };
  }

  // api.openout.mihoyo.com
  createAuthRequest(headers?: Record<string, any>, options?: Omit<RequestInit, 'body' | 'headers'>) {
    const baseUrl = getAuthDomain(this.env);
    const _headers = new Headers();
    if (headers) {
      Object.keys(headers).forEach((key) => {
        _headers.append(key, headers[key]);
      });
    }
    const _options = {
      ...this.config,
      ...options,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Mi-Clientid': getMoatClientId(this.env),
        'accept-language': this.lang,
        ...headers,
      },
    };
    return async (url: string, params: Record<string, any>) => {
      return request(`${baseUrl}${EnvMap[this.env].IAM_SRV_AUTH_PREFIX}${url}`, params, _options)
      .then((res) => {
        if (!this.workCodes.includes(res?.code)) {
          if (this.ctx.errObj) {
            this.ctx.errObj.code = res.code
            this.ctx.errObj.message = res.message
            this.ctx.errObj.url = url
            this.ctx.errObj.params = params
          }
        }
        return res
      }).catch(e => {
        this.ctx.errObj = e
        return e
      })
    };
  }

  createOtherRequest(headers?: Record<string, any>, options?: Omit<RequestInit, 'body' | 'headers'>) {
    const _headers = new Headers();
    if (headers) {
      Object.keys(headers).forEach((key) => {
        _headers.append(key, headers[key]);
      });
    }
    const _options = {
      ...this.config,
      ...options,
      method: 'POST',
      // headers: {
      //   'Content-Type': 'application/json',
      //   'X-Mi-Clientid': getMoatClientId(this.env),
      //   'accept-language': this.lang,
      //   ...headers,
      // },
    };
    return async (url: string, params: Record<string, any>) => {
      return request(`${url}`, params, _options)
      .then((res) => {
        if (!this.workCodes.includes(res?.code)) {
          if (this.ctx.errObj) {
            this.ctx.errObj.code = res.code
            this.ctx.errObj.message = res.message
            this.ctx.errObj.url = url
            this.ctx.errObj.params = params
          }
        }
        return res
      }).catch(e => {
        this.ctx.errObj = e
        return e
      })
    };
  }

  sessionCheck() {
    return this.request(sessionCheck, {});
  }

  async shimGetCorsTicket() {
    return await this.authRequest<{
      ticket: string;
    }>(shimGetCorsTicket, {clientId: getMoatClientId(this.env)});
  }

  async getPageConfig() {
    return await this.request<IV3LoginPageConfig>(pageConfigApi, {
      clientId: this.clientId
    })
  }

  async syncSessionToIam(ticket: string, clientId: string) {
    return await this.authRequest<null>(moatSilentLoginApi, {ticket, clientId});
  }

  async shimTicketLogin(ticket: string) {
    return await this.request<null>(shimTicketLogin, {ticket});
  }

  async waveSilenceLogin(otp: string, zt_ticket?: string) {
    return await this.request<{ ticket?: string }>(waveLogin, {
      verify_code: otp,
      otp_scene: 'InitialLogin',
      is_from_wave: true,
      zt_ticket
    });
  }

  async getJsTicket() {
    return await this.authRequest(getCorsTicketApi, {clientId: this.clientId})
  }

  async opTicketToToken(url: string, ticket: string) {
    return await this.otherRequest(url, {ticket})
  }

  async opCheckLogin(url: string) {
    return await this.otherRequest(url, {})
  }

  async logout() {
    return Promise.all([this.authRequest<null>(logOutApi, {}), this.request<null>(authnV3LogOutApi, {})]);
  }

  async checkLogin() {
    return await this.request<UserInfo>(checkLoginApi, {})
  }

  async canSwitch(domain: string) {
    return await this.request<CanSwitchResult>(canSwitchApi, {domain})
  }

  async switchUser(domain: string) {
    return await this.request<Response>(switchUserApi, {domain})
  }
}

export default Services;
