const sessionCheck = '/auth/session/check';

const shimGetCorsTicket = '/v1/shim/get_cors_ticket';

const shimTicketLogin = '/auth/login/shim/ticket_login';

const waveLogin = '/auth/login/wave/silent_login'

const moatSilentLoginApi = '/v1/shim/moat_silent_login';

const logOutApi = '/v1/session/logout';

const authnV3LogOutApi = '/sso_logout';

const getCorsTicketApi = '/v1/oauth2/get_cors_ticket';

const checkLoginApi = '/public/check_login'

const canSwitchApi = '/dev/can_switch'

const switchUserApi = '/dev/switch_user'

const pageConfigApi = '/auth/login_page_config'

const getWaveJsTicketAndAppIdApi = '/auth/get_wave_ticket'

export {
  getWaveJsTicketAndAppIdApi,
  pageConfigApi,
  sessionCheck,
  shimGetCorsTicket,
  shimTicketLogin,
  waveLogin,
  moatSilentLoginApi,
  logOutApi,
  authnV3LogOutApi,
  getCorsTicketApi,
  checkLoginApi,
  canSwitchApi,
  switchUserApi
};
