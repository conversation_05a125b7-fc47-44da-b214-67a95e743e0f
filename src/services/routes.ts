const sessionCheck = '/auth/session/check';

const shimGetCorsTicket = '/v1/shim/get_cors_ticket';

const shimTicketLogin = '/auth/login/shim/ticket_login';

const waveLogin = '/auth/login/wave/silent_login'

const moatSilentLoginApi = '/v1/shim/moat_silent_login';

const logOutApi = '/v1/session/logout';

const authnV3LogOutApi = '/sso_logout';

const getCorsTicketApi = '/v1/oauth2/get_cors_ticket';

const checkLoginApi = '/public/check_login'

const canSwitchApi = '/dev/can_switch'

const switchUserApi = '/dev/switch_user'

const pageConfigApi = '/auth/login_page_config'

export {
  pageConfigApi,
  sessionCheck,
  shimGetCorsTicket,
  shimTicketLogin,
  waveLogin,
  moatSilentLogin<PERSON>pi,
  logOutApi,
  authnV3LogOut<PERSON>pi,
  getCorsTicket<PERSON>pi,
  checkLogin<PERSON><PERSON>,
  canSwitchApi,
  switchUserApi
};
