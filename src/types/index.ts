import Callback from "../core/callback";
import * as HoyowaveJsApi from '@hoyowave/jsapi';

export type Env = 'test' | 'uat' | 'prod' | 'pp';

export type Lang = 'zh-CN' | 'en-US';

export type Scene = 'login' | 'second_mfa';

export interface Response<T = any> {
  code: number;
  message: string;
  data: T;
  error: boolean;
  success: boolean;
}

export interface ResultPayload {
  errCode: number;
  errMsg?: string;
}

export interface IInitConfig {
  clientId: string;
  lang?: Lang;
  showLang?: boolean;
  general?: boolean;
  mask?: boolean;
  loginPageConfigCode?: string;
  appKey?: string;
  rootElement?: HTMLElement;
  env: Env;
  waveOtp?: string;
  getOTPToken?: (params?: { appId: string }) => Promise<{ otp: string }>;
  showPage?: boolean; // 是否渲染 iframe
  messageSlot?: MessageSlot; // 插槽
  handleLang?: (lang: Lang) => void;
  scene?: Scene;
  wrapperStyle?: string;
  showMfaCloseIcon?: boolean;
  handleMfaClose?: () => void;
  hoyowaveJsApi?: typeof HoyowaveJsApi;
}

export enum IV3NETWORK_TYPE {
  Intranet = 'Intranet',
  Extranet = 'Extranet',
}

export interface IV3Agreement {
  id: string;
  protocol_name: string;
  protocol_content: string;
}

export interface IV3LoginPageProtocol {
  agreement_prefix: string; // 协议前置文案
  agreement_list: IV3Agreement[];
}

export enum IV3Method {
  AccountPwd = 'AccountPwd', // 账密
  SMS = 'SMS', // 短信
  WaveQR = 'WaveQR', // Wave扫码
  Email = 'Email', // 邮箱
  WaveQuickLogin = 'WaveQuickLogin', // Wave快捷登录
  TOTP = 'TOTP', // TOTP
  SecondPwd = 'SecondPwd',
  'identity:email' = 'identity:email', // 个人中心邮箱二级认证
  'identity:sms' = 'identity:sms', // 个人中心手机二级认证
  'iOS' = 'iOS',
  'Android' = 'Android',
}

export interface IV3AuthMethodConfigs {
  method: IV3Method;
  require_captcha: boolean; // 是否需要人机交互
  config?: {
    app_id?: string;
  };
}

export interface IV3Multilingual {
  accept_language: Lang;
  language: string;
}

export type IV3ThirdPartyAuthMethodConfigs = {
  app_icon?: string; // 第三方应用图标
  app_name?: string; // 第三方应用名称
  auth_uri: string; // 第三方登录回调地址
};

export interface IV3LoginPageConfig {
  app_icon: string;
  app_name: string;
  bottom_pic: string;
  top_pic: string;
  enable_forgot_pwd: boolean; // 配置忘记密码
  enable_user_register: boolean; // 配置注册
  network: IV3NETWORK_TYPE; // 网络类型
  login_page_protocol: IV3LoginPageProtocol;
  auth_method_configs: IV3AuthMethodConfigs[];
  prefer_login_method?: 'WaveQuickLogin';
  multilingual: IV3Multilingual[];
  session_expiration: string;
  welcome: string;
  prompt?: string;
  third_party_auth_method_configs?: IV3ThirdPartyAuthMethodConfigs[];
  operation_banner_pic?: string; // 运营banner图
  check_agreement_privacy?: boolean; // 是否勾选隐私协议
}

export interface IError {
  code: number;
  message: string;
  browser: boolean;
}

export type SuccessCb = () => void;
export type ErrorCb = (errInfo: IError) => void;
export type FailCb = (failInfo: string) => void;

export interface IamObj {
  onSuccess: (cb: SuccessCb) => void;
  onError: (cb: ErrorCb) => void;
  onFail: (cb: FailCb) => void;
}

export interface Ctx {
  isLogin?: boolean;
  isWaveEnv?: boolean;
  isRender?: boolean;
  config?: IInitConfig
  callback?: Callback
  errObj?: {
    code?: number | null
    message?: string | null
    url?: string
    params?: any
  }
  extraProps?: ExtraProps
  iframeWindow?: Window
  clean?: () => void
}

export type MessageSlot = {
  WAVE_CROSS_TENANT: string
}

export interface ExtraProps {
  [key: string]: any
}

export enum APP_TYPE {
  IFRAME,
  QIANKUN_SUB_APP,
  SSR,
  OTHER
}


export enum APP_THEME {
  LIGHT = 'light',
  DARK = 'dark'
}

export type AppConfigArg = {
  lang?: Lang;
  theme?: APP_THEME;
  baseURL?: string
  env?: Env
  [key: string]: any
};

export enum LogoutTypeEnum {
  SSO,
  API,
}

export enum OpType {
  Op = 'op',
  OpOuter = 'op-outer',
  OpHoyoverse = 'op-hoyoverse',
  OpHoyoverseOuter = 'op-hoyoverse-outer',
  OpHoyoverseInc = 'op-hoyoverse-inc',
  OpHoyoverseIncOuter = 'op-hoyoverse-inc-outer',
}


export type UserInfo = {
  mi_id: string;
  name: string;
  name_en_abbr: string;
  domain: string;
  wave_avatar_url: string;
  enterprise_emails: string[];
  [key: string]: any
}

export type CanSwitchResult = {
  can_switch: boolean;
}

export type IV3WaveJsTicketRes = {
  ticket: string;
  app_id: string;
};


