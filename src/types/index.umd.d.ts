import {APP_TYPE, AppConfigArg, Env, IamObj, IInitConfig, Lang, OpType, Response, UserInfo} from "./index";

declare global {
  namespace iamNext {
    function initIamLogin(config: IInitConfig, callback: (iamObj: IamObj) => void): void;

    class ClientIdManage {
      constructor(clientId?: string);

      private detectAppType: () => APP_TYPE;

      private getClientIdByQueryParameters: () => string;

      private getClientIdByMetaData: () => string;

      getClientId: () => string;
    }

    class AppConfig {
      constructor(env: Env, config?: AppConfigArg);

      getAppConfig: () => AppConfigArg & { iamNext: number };
    }

    class Logout {
      constructor(env: Env, lang?: Lang);

      public logoutBySSO: () => void;
      public logoutByApi: () => Promise<[Response<null>, Response<null>]>;
    }

    class SyncToOp {
      constructor(env: Env, clientId: string, type: OpType, appKey?: string);

      public syncToOp: () => Promise<boolean>;
    }

    class Op {
      constructor(env: Env, clientId: string, type: OpType, appKey?: string);

      public syncToOp: () => Promise<boolean>;
      public checkLogin: () => Promise<Response<UserInfo>>;

      public opCheckLogin(): Promise<any>

      public syncAndOpCheckLogin(): Promise<any>

      public opCheckLoginV2(): Promise<any>
    }

    class SwitchUser {
      constructor(env: Env, clientId: string, domain: string);

      private parametersValidation: () => void;

      private envValidation: () => void;

      canSwitchUser(): Promise<boolean>;

      switchUser(): Promise<{ result: boolean; message: string }>;
    }
  }
}
