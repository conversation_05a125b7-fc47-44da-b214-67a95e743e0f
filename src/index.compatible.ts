import 'core-js/stable';
import 'regenerator-runtime/runtime';

import 'whatwg-fetch'

export {default as initIamLogin} from './core';
export {default as ClientIdManage} from './core/clientId';
export {default as AppConfig} from './app-config';
export {default as Logout} from './logout';
export {default as SyncToOp} from './sync-to-op'
export {default as Op} from './op'
export {default as SwitchUser} from './switch-user'
