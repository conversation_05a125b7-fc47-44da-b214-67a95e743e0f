import SyncToOp from "@/sync-to-op";
import {Env, OpType} from "@/types";
import {getOpConfig} from "@/utils/opConfig";

class Op extends SyncToOp {
  constructor(env: Env, clientId: string, type = OpType.Op, appKey?: string) {
    super(env, clientId, type, appKey);
  }

  public async checkLogin() {
    return await this.services.checkLogin()
  }

  public async opCheckLogin() {
    const config = getOpConfig(this.type, this.env)
    const url = config.checkLoginApi
    if (url) {
      return await this.services.opCheckLogin(url)
    }
    return Promise.reject('checkLoginUrl is required');
  }

  public async syncAndOpCheckLogin() {
    const syncResult = await this.sync()
    if (syncResult) {
      return await this.opCheckLogin()
    }
    return Promise.reject('sync failed')
  }

  public async opCheckLoginV2() {
    try {
      const data = await this.opCheckLogin()
      // op 登录态失效
      if (data?.code === -100) {
        // 尝试同步
        const syncResult = await this.sync()
        if (syncResult) {
          // 同步成功后再次检查登录态
          const _data = await this.opCheckLogin()
          // return 响应体
          return _data?.data
        }
        // 同步失败 return 响应体
        return data.data
      }
      // op 登录态正常或者其他状态码,return 原始响应体
      return data.data
    } catch (_) {
      return Promise.reject('opCheckLoginV2 failed')
    }
  }
}

export default Op
