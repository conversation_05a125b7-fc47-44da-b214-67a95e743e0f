import {Env, Lang} from "@/types";
import {getRedirectUri, getWebsiteDomain} from "@/utils";
import Services from "@/services";

class Logout {
  constructor(private readonly env: Env, private readonly lang?: Lang) {
    if (!this.env) {
      throw new Error('env is required');
    }
  }

  public logoutBySSO() {
    const pathname = '/logout';
    const urlString = getWebsiteDomain(this.env) + pathname;
    const url = new URL(urlString)
    const redirectUri = decodeURIComponent(getRedirectUri());
    url.searchParams.append('postLogoutRedirectUri', redirectUri);
    url.searchParams.append('lang', this.lang || 'zh-CN');
    window.location.href = url.toString();
  }

  public logoutByApi() {
    const services = new Services(this.env, '', this.lang, {});
    return services.logout()
  }
}

export default Logout;
