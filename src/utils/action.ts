import {onPostMessage, replyOnPostMessage, requestPostMessage} from '@/message';
import {ACTION} from '@/utils/constant';
import {ExtraProps, Lang} from "@/types";
import {destroy} from "@/core/iframe";

const requestLoadMessage = (ctx: Window, targetOrigin: string) => {
  return requestPostMessage({
    target: ctx,
    eventName: ACTION.LOAD_SUCCESS,
    data: {
      init: true,
    },
    targetOrigin,
  });
};

const subscribeSuccessCallback = (cb: () => void) => {
  return onPostMessage({
    eventName: ACTION.SUCCESS_CALLBACK,
    callback: cb,
  });
};

const postLocation = (url: string) => {
  return replyOnPostMessage({
    eventName: ACTION.GET_LOCATION,
    callback: () => {
      console.log('GET_LOCATION', url)
      return {url};
    },
  });
};

const changeLocation = () => {
  return onPostMessage({
    eventName: ACTION.CHANGE_LOCATION,
    callback: (event: MessageEvent) => {
      console.log('CHANGE_LOCATION', event)
      if (event?.data?.data?.url) {
        window.location.href = event?.data?.data?.url;
      }
    },
  });
};

const openLocation = () => {
  return onPostMessage({
    eventName: ACTION.OPEN_LOCATION,
    callback: (event: MessageEvent) => {
      if (event?.data?.data?.url) {
        console.log('OPEN_LOCATION', event)
        window.open(event?.data?.data?.url, '_blank');
      }
    },
  });
};

const reloadLocation = () => {
  return onPostMessage({
    eventName: ACTION.RELOAD_LOCATION,
    callback: () => {
      console.log('RELOAD_LOCATION')
      window.location.reload();
    },
  });
};

const sendRequestError = (code: number, message: string, url: string, params: Record<string, any>) => {
  console.log('REQUEST_ERROR', {
    code,
    message,
    url,
    params
  })
  return replyOnPostMessage({
    eventName: ACTION.REQUEST_ERROR,
    callback: () => {
      return {
        code,
        message,
        url,
        params
      };
    }
  })
}

const sendExtraProps = (extraProps: ExtraProps) => {
  return replyOnPostMessage({
    eventName: ACTION.SEND_EXTRA_PROPS,
    callback: () => {
      console.log('SEND_EXTRA_PROPS', extraProps)
      return {extraProps};
    },
  });
}

const handleLang = (handle: (lang: Lang) => void) => {
  onPostMessage({
    eventName: ACTION.CHANGE_LANG,
    callback: (event: MessageEvent) => {
      if (event?.data?.data?.lang) {
        handle(event.data.data.lang);
      }
    }
  })
}

const handleDestroy = (handle: () => void, clean: () => void) => {
  return onPostMessage({
    eventName: ACTION.DESTROY,
    callback: () => {
      clean?.()
      console.log('DESTROY')
      destroy();
      handle?.()
    }
  })
}

export {
  handleLang,
  requestLoadMessage,
  subscribeSuccessCallback,
  postLocation,
  changeLocation,
  openLocation,
  reloadLocation,
  sendRequestError,
  sendExtraProps,
  handleDestroy
};
