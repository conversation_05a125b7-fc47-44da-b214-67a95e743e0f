class Cookie {
  constructor() {
  }

  private checkDocument() {
    return typeof document !== 'undefined';
  }

  public get(name: string) {
    if (typeof document === 'undefined') {
      return '';
    }

    if (typeof name !== 'string' || !name.trim()) {
      return '';
    }

    const cookies = document.cookie.split(';') || [];
    for (const cookie of cookies) {
      const [key, ...rest] = cookie.split('=');
      if (key.trim() === name) {
        return decodeURIComponent(rest.join('='));
      }
    }

    return '';
  }
}

export default Cookie;
