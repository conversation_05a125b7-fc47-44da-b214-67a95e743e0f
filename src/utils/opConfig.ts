import {Env, OpType} from "@/types";

const opConfig = {
  test: {
    sysLoginApi: 'https://devop-test-api.office.mihoyo.com/employee_v2/op/get_iam_sub_token_by_ticket',
    checkLoginApi: 'https://devop-test-api.office.mihoyo.com/employee_v2/op/check_login',
  },
  uat: {
    sysLoginApi: 'https://devop-takumi.office.mihoyo.com/employee_v2/op/get_iam_sub_token_by_ticket',
    checkLoginApi: 'https://devop-takumi.office.mihoyo.com/employee_v2/op/check_login',
  },
  pp: {
    sysLoginApi: 'https://preop-takumi.mihoyo.com/employee_v2/op/get_iam_sub_token_by_ticket',
    checkLoginApi: 'https://preop-takumi.mihoyo.com/employee_v2/op/check_login',
  },

  prod: {
    checkLoginApi: 'https://op-takumi.office.mihoyo.com/employee_v2/op/check_login',
    sysLoginApi: 'https://op-takumi.office.mihoyo.com/employee_v2/op/get_iam_sub_token_by_ticket',
  },
};

const opOuterConfig = {
  test: {
    checkLoginApi: 'https://devop-test-api.office.mihoyo.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://devop-test-api.office.mihoyo.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
  uat: {
    checkLoginApi: 'https://devapi-takumi.mihoyo.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://devapi-takumi.mihoyo.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
  prod: {
    checkLoginApi: 'https://public-operation-common.mihoyo.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://public-operation-common.mihoyo.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
};

const opHoyoverseConfig = {
  test: {
    checkLoginApi: 'https://devop-test-takumi.office.hoyoverse.com/employee_v2/op/check_login',
    sysLoginApi: 'https://devop-test-takumi.office.hoyoverse.com/employee_v2/op/get_iam_sub_token_by_ticket',
  },
  uat: {
    checkLoginApi: 'https://devop-takumi.office.hoyoverse.com/employee_v2/op/check_login',
    sysLoginApi: 'https://devop-takumi.office.hoyoverse.com/employee_v2/op/get_iam_sub_token_by_ticket',
  },
  prod: {
    checkLoginApi: 'https://op-takumi.office.hoyoverse.com/employee_v2/op/check_login',
    sysLoginApi: 'https://op-takumi.office.hoyoverse.com/employee_v2/op/get_iam_sub_token_by_ticket',
  },
};

const opHoyoverseOuterConfig = {
  test: {
    checkLoginApi: 'https://devop-test-takumi.office.hoyoverse.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://devop-test-takumi.office.hoyoverse.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
  uat: {
    checkLoginApi: 'https://testing-sg-public-pubapi.hoyoverse.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://testing-sg-public-pubapi.hoyoverse.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
  prod: {
    checkLoginApi: 'https://public-operation-common.hoyoverse.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://public-operation-common.hoyoverse.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
};

const opHoyoverseIncConfig = {
  test: {
    checkLoginApi: 'https://op-operation-al-sg-testing.hoyoverse-inc.com/employee_v2/op/check_login',
    sysLoginApi: 'https://op-operation-al-sg-testing.hoyoverse-inc.com/employee_v2/op/get_iam_sub_token_by_ticket',
  },
  uat: {
    checkLoginApi: 'https://op-operation-al-sg-testing.hoyoverse-inc.com/employee_v2/op/check_login',
    sysLoginApi: 'https://op-operation-al-sg-testing.hoyoverse-inc.com/employee_v2/op/get_iam_sub_token_by_ticket',
  },
  prod: {
    checkLoginApi: 'https://op-operation-al-sg.hoyoverse-inc.com/employee_v2/op/check_login',
    sysLoginApi: 'https://op-operation-al-sg.hoyoverse-inc.com/employee_v2/op/get_iam_sub_token_by_ticket',
  },
};

const opHoyoverseIncOuterConfig = {
  test: {
    checkLoginApi: 'https://op-operation-al-sg-testing.hoyoverse-inc.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://op-operation-al-sg-testing.hoyoverse-inc.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
  prod: {
    checkLoginApi: 'https://op-takumi.hoyoverse-inc.com/employee_v2/outer/check_login',
    sysLoginApi: 'https://op-takumi.hoyoverse-inc.com/employee_v2/outer/get_iam_sub_token_by_ticket',
  },
};

const getOpConfig = (opType: OpType, env: Env) => {
  switch (opType) {
    case 'op':
    default:
      return opConfig[env] || {};
    case 'op-outer':
      return opOuterConfig[env] || {};
    case 'op-hoyoverse':
      return opHoyoverseConfig[env] || {};
    case 'op-hoyoverse-outer':
      return opHoyoverseOuterConfig[env] || {};
    case 'op-hoyoverse-inc':
      return opHoyoverseIncConfig[env] || {};
    case 'op-hoyoverse-inc-outer':
      return opHoyoverseIncOuterConfig[env] || {};
  }
};

export {
  getOpConfig
}

