import {HOYOVERSE_DOMAIN, HOYOVERSE_INC_DOMAIN, IFRAME_ID, IFRAME_NAME, MIHOYO_DOMAIN} from './constant';
import type {Env} from '../types';
import {IInitConfig} from '../types';
import EnvMap from './env';
import Cookie from './cookie';

const getRedirectUri = () => {
  const url = window.location.href;
  return encodeURIComponent(url);
};

const getOrigin = () => {
  const url = new URL(window.location.href);
  return url.origin;
};

const getTLD = () => {
  const domain = window.location.hostname;
  if (/^(?:\d{1,3}\.){3}\d{1,3}$/.test(domain)) {
    return MIHOYO_DOMAIN;
  }
  if (/^\[[0-9a-fA-F:]+]$/.test(domain)) {
    return MIHOYO_DOMAIN;
  }
  if (domain === 'localhost') {
    return MIHOYO_DOMAIN;
  }
  return domain.match(/(?:[^.]+\.)?([^.]+\.[^.]+)$/)?.[1] ?? MIHOYO_DOMAIN;
};

const getBaseUrl = (env: Env) => {
  const tld = getTLD();
  switch (tld) {
    case MIHOYO_DOMAIN:
    default:
      return EnvMap[env].IAM_SRV_DOMAIN_CN;
    case HOYOVERSE_DOMAIN:
      return EnvMap[env].IAM_SRV_DOMAIN_HOYOVERSE;
    case HOYOVERSE_INC_DOMAIN:
      return EnvMap[env].IAM_SRV_DOMAIN_GLOBAL;
  }
};

const getAuthDomain = (env: Env) => {
  const tld = getTLD();
  switch (tld) {
    case MIHOYO_DOMAIN:
    default:
      return EnvMap[env].IAM_SRV_AUTH_DOMAIN_CN;
    case HOYOVERSE_DOMAIN:
      return EnvMap[env].IAM_SRV_AUTH_DOMAIN_HOYOVERSE;
    case HOYOVERSE_INC_DOMAIN:
      return EnvMap[env].IAM_SRV_AUTH_DOMAIN_GLOBAL;
  }
};

const getWebsiteDomain = (env: Env) => {
  const tld = getTLD();
  switch (tld) {
    case MIHOYO_DOMAIN:
    default:
      return EnvMap[env].IAM_WEBSITE_CN;
    case HOYOVERSE_DOMAIN:
      return EnvMap[env].IAM_WEBSITE_HOYOVERSE;
    case HOYOVERSE_INC_DOMAIN:
      return EnvMap[env].IAM_WEBSITE_GLOBAL;
  }
};

const getUrl = (domain: string, path = '/login', config: IInitConfig) => {
  // 过滤掉 config 中的 handleLang, messageSlot, getOTPToken， 这些在链接上不需要的属性
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const {env: _, handleLang, messageSlot, getOTPToken, handleMfaClose, ...rest} = config;
  const url = new URL(domain + path);
  const redirectUri = getRedirectUri();
  const extraParams = {
    redirectUri,
    callbackType: 'h5',
  };
  Object.entries({
    ...rest,
    ...extraParams,
  }).forEach(([key, value]) => {
    url.searchParams.append(key, value.toString());
  });
  return url;
};

const createIframe = (src: string, rootElement: HTMLElement, style = ''): Promise<Window> => {
  if (document.getElementById('iam-login-next')) {
    document.getElementById('iam-login-next')?.remove();
  }
  const _style = `${style}border: none; width: 100%; height: 100%; position: fixed; top: 0; left: 0; z-index: 9999;background: #fff;`
  return new Promise((resolve, reject) => {
    const element = document.createElement('iframe');
    element.setAttribute('src', src);
    element.setAttribute('name', IFRAME_NAME);
    element.setAttribute('id', IFRAME_ID);
    element.setAttribute('frameBorder', '0');
    element.setAttribute('allowfullscreen', 'true');
    element.setAttribute('scrolling', 'auto');
    element.setAttribute('style', _style);
    element.onload = () => {
      if (element.contentWindow) {
        resolve(element.contentWindow!);
        return;
      }
      reject('iframe contentWindow is null');
    };
    rootElement.appendChild(element);
  });
};

const getShim = (env: Env) => {
  const cookie = new Cookie();
  if (env === 'prod') {
    return cookie.get('sso_shim');
  }
  const key = `sso_shim_${env}`;
  return cookie.get(key);
};

const getMoatClientId = (env: Env) => {
  const tld = getTLD();
  switch (tld) {
    case MIHOYO_DOMAIN:
    default:
      return EnvMap[env].MOAT_CLIENT_ID_CN;
    case HOYOVERSE_DOMAIN:
      return EnvMap[env].MOAT_CLIENT_ID_CN;
    case HOYOVERSE_INC_DOMAIN:
      return EnvMap[env].MOAT_CLIENT_ID_GLOBAL;
  }
};

const getIsWaveEnv = () => {
  const lowerCaseUa = navigator.userAgent.toLowerCase();
  return lowerCaseUa.includes('hoyowave');
};

const deepEqual = (config1: IInitConfig, config2: IInitConfig) => {
  if (config1 === config2) {
    return true
  }
  if (typeof config1 !== 'object' || typeof config2 !== 'object' || config1 === null || config2 === null) {
    return false
  }
  const keys1 = Object.keys(config1);
  const keys2 = Object.keys(config2);
  if (keys1.length !== keys2.length) {
    return false; // 如果两个对象的键数量不同，返回 false
  }

  for (const key of keys1) {
    const val1 = config1[key];
    const val2 = config2[key];

    const areObjects = typeof val1 === 'object' && typeof val2 === 'object' && val1 !== null && val2 !== null;
    const areFunctions = typeof val1 === 'function' && typeof val2 === 'function';

    if (areObjects && !deepEqual(val1, val2)) {
      return false;
    } else if (areFunctions && val1.toString() !== val2.toString()) {
      return false;
    } else if (!areObjects && !areFunctions && val1 !== val2) {
      return false;
    }
  }
  return true;
}

export {
  getRedirectUri,
  getOrigin,
  getTLD,
  getBaseUrl,
  getWebsiteDomain,
  getUrl,
  createIframe,
  getShim,
  getAuthDomain,
  getMoatClientId,
  getIsWaveEnv,
  deepEqual
};
