import { Env } from '../types';
// import { isMobile } from './index';

const EnvMap = {
  test: 'https://south-gatetest.mihoyo.com/sniffing/collect',
  uat: 'https://south-gateuat.mihoyo.com/sniffing/collect',
  pp: 'https://south-gateuat.mihoyo.com/sniffing/collect',
  prod: 'https://south-gate.mihoyo.com/sniffing/collect',
};

class Log {
  bizId = 138;
  api: string;
  deviceEnv: Record<string, any>;

  constructor(env: Env) {
    this.api = EnvMap[env];
    this.deviceEnv = {
      ua: navigator.userAgent,
      isWave: navigator.userAgent.toLowerCase().includes('hoyowave'),
      // device: isMobile() ? 'mobile' : 'pc',
      domain: window.location.hostname,
      pathname: window.location.pathname,
      ts: new Date().getTime(),
    };
  }

  send(data: Record<string, any>) {
    const body = JSON.stringify({
      bizId: this.bizId,
      data,
    });

    fetch(this.api, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body,
    }).then();
  }
}

export default Log;
