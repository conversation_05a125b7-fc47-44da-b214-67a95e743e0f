import {AppConfigArg, Env} from "@/types";
import {getBaseUrl} from "@/utils";

class AppConfig {

  constructor(private readonly env: Env, private readonly config?: AppConfigArg) {
    if (!this.env) {
      throw new Error('env is required');
    }
  }

  public getAppConfig(): AppConfigArg & { iamNext: number } {
    const _env = this.env || 'prod'
    return {
      ...this.config,
      env: _env,
      iamNext: 1,
      baseURL: getBaseUrl(_env)
    };
  }
}

export default AppConfig;

