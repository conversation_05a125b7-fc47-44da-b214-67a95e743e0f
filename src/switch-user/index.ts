import {Env} from "@/types";
import Services from "@/services";

class SwitchUser {
  private services: Services;

  constructor(private readonly env: Env, private readonly clientId: string, private readonly domain: string) {
    this.parametersValidation()
    this.envValidation()
    this.services = new Services(this.env, this.clientId, "zh-CN", {})
  }

  private parametersValidation() {
    if (!this.env) {
      throw new Error('env is required');
    }
    if (!this.clientId) {
      throw new Error('clientId is required');
    }
    if (!this.domain) {
      throw new Error('domain is required');
    }
  }

  private envValidation() {
    if (this.env !== "test" && this.env !== "uat") {
      throw new Error('env is invalid');
    }
  }

  public async canSwitchUser() {
    const data = await this.services.canSwitch(this.domain);
    if (data?.code === 0) {
      return data?.data?.can_switch
    }
    return false
  }

  public async switchUser() {
    const data = await this.services.switchUser(this.domain);
    return {
      result: data?.code === 0,
      message: data?.message
    }
  }

}

export default SwitchUser
