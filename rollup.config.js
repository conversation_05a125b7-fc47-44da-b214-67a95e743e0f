// rollup.config.js
import babel from '@rollup/plugin-babel';
import nodeResolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
import { version } from './package.json';
import terser from '@rollup/plugin-terser';
import filesize from 'rollup-plugin-filesize';
import copy from 'rollup-plugin-copy';


import path from 'path';

const banner = `/**!
 * ${'iam-next-sdk'} ${version}
 * 2011 - ${new Date().getFullYear()} miHoYo. All Rights Reserved
 *
 * Copyright (c) ${new Date().getFullYear()} miHoYo IAM Team
 *
 *
 * Licensed under the UNLICENSED license
 */`;

const resolveFile = function (filePath) {
  // eslint-disable-next-line no-undef
  return path.join(__dirname, filePath);
};

const config = {
  umd: {
    input: 'src/index.ts',
    output: {
      file: resolveFile('dist/index.min.js'),
      format: 'umd',
      name: 'iamNext',
      sourcemap: false,
    },
    pluginsConfig: {
      babelConfig: {
        exclude: 'node_modules/**', // 排除 node_modules 目录
        babelHelpers: 'bundled', // Babel 帮助函数的加载方式
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false, // 指定转译模块类型
              targets: {
                ie: 11,
                chrome: 49,
                ios: 10,
              },
              useBuiltIns: false, // 或者 'usage'，用于 polyfill
            },
          ],
        ],
        plugins: ['@babel/plugin-transform-runtime', '@babel/plugin-proposal-class-properties'],
      },
      tsConfig: {
        declaration: false,
        declarationMap: false,
        target: 'es5',
      },
    },
  },
  compatible: {
    input: 'src/index.compatible.ts',
    output: {
      file: resolveFile('dist/index.compatible.min.js'),
      format: 'umd',
      name: 'iamNext',
      sourcemap: false,
    },
    pluginsConfig: {
      babelConfig: {
        exclude: 'node_modules/**', // 排除 node_modules 目录
        babelHelpers: 'bundled', // Babel 帮助函数的加载方式
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false, // 指定转译模块类型
              targets: {
                ie: '> 10',
                '> 0.5%': true,
                'not dead': true,
              },
              useBuiltIns: 'entry', // 或者 'usage'，用于 polyfill
              corejs: 3, // 使用 core-js 版本 3
            },
          ],
        ],
        plugins: ['@babel/plugin-transform-runtime', '@babel/plugin-proposal-class-properties'],
      },
      tsConfig: {
        declaration: false,
        declarationMap: false,
        target: 'es5',
      },
    },
  },
  esm: {
    input: 'src/index.ts',
    output: {
      file: resolveFile('dist/index.esm.min.js'),
    },
    pluginsConfig: {
      babelConfig: {
        babelHelpers: 'bundled', // Babel 帮助函数的加载方式
        exclude: 'node_modules/**', // 排除 node_modules 目录
        presets: [
          [
            '@babel/preset-env',
            {
              modules: false, // 指定转译模块类型
              targets: {
                esmodules: true,
              },
            },
          ],
        ],
        plugins: ['@babel/plugin-transform-runtime', '@babel/plugin-proposal-class-properties'],
      },
      tsConfig: {},
    },
  },
};

const genConfig = (target) => {
  return config[target];
};

const _config = genConfig(process.env.TARGET);
const {
  input,
  output,
  pluginsConfig: {babelConfig, tsConfig},
} = _config;

export default {
  input,
  output: {
    ...output,
    banner, // 生成的 banner
  },
  plugins: [
    nodeResolve(), // 解析 Node.js 模块
    commonjs(), // 处理 CommonJS 模块
    {
      enforce: 'pre',
      ...babel({
        ...babelConfig,
      }),
    },
    typescript({...tsConfig}), // 处理 TypeScript
    terser(),
    filesize({
      showBeforeSizes: 'build',
    }),
    process.env.TARGET === 'esm' && copy({
      targets: [
        {
          src: 'src/types/index.umd.d.ts',
          dest: 'dist/types/types',
        },
      ],
      // 可选：控制是否清理目标文件夹
      verbose: true, // 打印复制过程
      hook: 'writeBundle', // 在写入输出文件后触发
    }),
  ],
};
