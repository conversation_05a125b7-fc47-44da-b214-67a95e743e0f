# @iam/init-sdk

## 0.1.9

### Patch Changes

- feat: logout

## 0.1.8

### Patch Changes

- upd: sdk@0.1.8

## 0.1.7

### Patch Changes

- fix: cookie 取值问题

## 0.1.6

### Patch Changes

- fix: 删除多余日志

## 0.1.5

### Patch Changes

- fix: 同步登录态 clientId 参数

## 0.1.4

### Patch Changes

- fix: wave 免等也需要 SyncSessionToMoat

## 0.1.3

### Patch Changes

- feat: prod

## 0.1.2

### Patch Changes

- release:0814

## 0.1.1

### Patch Changes

- sso_shim 生产标

## 0.1.0

### Minor Changes

- release: 0807

## 0.0.3

### Patch Changes

- fix: prod env

## 0.0.2

### Patch Changes

- c150fbb: feat: 测试 umd
- fccc12e: fix: 修复 destroy 时序错误
- 36a19c3: feat: test refresh
- 184271b: fix: cache
- 5bfd0db: feat: memory cache
- 2368526: feat: err 回调
- 7b19619: feat: 更新 dev IAM_SRV_PREFIX_CN 环境变量
- ee49fa5: feat: ie11 兼容
- cb77738: feat: test umd
- 1050efe: feat: cache
- 405af18: feat: cache
- feat: prod latest
- 8bcfce2: feat: 测试 banner
- 1e7d92f: feat: dev first publish
- 1f6e263: fix: env
- 561839b: feat: test refresh
- 0bbcc63: feat: test banner
- 74bf52b: feat: test umd
- 4387fe7: feat: init test
- 33e6006: feat: 删除 session/check 逻辑
- 5cde48b: feat: render 过滤环境标识
- 561839b: feat: refresh
- 36a19c3: fix: test IAM_WEBSITE_CN
- b549aa5: feat: test cache
- bb5ba3c: feat: umd name iamNext
- ee49fa5: feat: ie11
- 883a516: feat:240717
- e2019ec: fix: env
- 36a19c3: feat: 测试
- 4d89253: Update .env.dev
- 05cdd97: feat: addBanner
- 7d8063d: fix: cache
- 52abe83: fix: filename
- 03cd7f2: feat: 新增 iframe destroy 函数，回调结束后 destroy
- f886c14: feat: 更新 test IAM_SRV_PREFIX_CN 环境变量
- 36a19c3: feat: test refresh
- c3e22f3: feat: test cache

## 0.0.2-test.55

### Patch Changes

- feat: ie11 兼容

## 0.0.2-test.54

### Patch Changes

- feat: ie11

## 0.0.2-test.32

### Patch Changes

- feat: err 回调

## 0.0.2-test.31

### Patch Changes

- fix: cache

## 0.0.2-test.30

### Patch Changes

- feat: cache

## 0.0.2-test.29

### Patch Changes

- feat: render 过滤环境标识

## 0.0.2-test.28

### Patch Changes

- feat: refresh

## 0.0.2-test.27

### Patch Changes

- feat: test refresh

## 0.0.2-test.26

### Patch Changes

- fix: test IAM_WEBSITE_CN

## 0.0.2-test.25

### Patch Changes

- feat: 测试

## 0.0.2-test.24

### Patch Changes

- feat: test refresh

## 0.0.2-test.23

### Patch Changes

- feat: test refresh

## 0.0.2-test.23

### Patch Changes

- feat:240717

## 0.0.2-test.22

### Patch Changes

- fix: filename

## 0.0.2-test.21

### Patch Changes

- 4387fe7: feat: init test

## 0.0.2-dev.20

### Patch Changes

- b549aa5: feat: test cache

## 0.0.2-dev.19

### Patch Changes

- c3e22f3: feat: test cache

## 0.0.2-dev.18

### Patch Changes

- 7d8063d: fix: cache

## 0.0.2-dev.17

### Patch Changes

- 1050efe: feat: cache

## 0.0.2-dev.16

### Patch Changes

- 5bfd0db: feat: memory cache

## 0.0.2-dev.15

### Patch Changes

- e2019ec: fix: env

## 0.0.2-dev.14

### Patch Changes

- 1f6e263: fix: env

## 0.0.2-dev.13

### Patch Changes

- bb5ba3c: feat: umd name iamNext

## 0.0.2-dev.12

### Patch Changes

- 0bbcc63: feat: test banner

## 0.0.2-dev.11

### Patch Changes

- 8bcfce2: feat: 测试 banner

## 0.0.2-dev.10

### Patch Changes

- 05cdd97: feat: addBanner

## 0.0.2-dev.9

### Patch Changes

- cb77738: feat: test umd

## 0.0.2-dev.8

### Patch Changes

- 74bf52b: feat: test umd

## 0.0.2-dev.7

### Patch Changes

- c150fbb: feat: 测试 umd

## 0.0.2-dev.6

### Patch Changes

- fccc12e: fix: 修复 destroy 时序错误

## 0.0.2-dev.5

### Patch Changes

- 03cd7f2: feat: 新增 iframe destroy 函数，回调结束后 destroy

## 0.0.2-dev.4

### Patch Changes

- feat: 删除 session/check 逻辑

## 0.0.2-dev.3

### Patch Changes

- Update .env.dev

## 0.0.2-dev.2

### Patch Changes

- feat: 更新 test IAM_SRV_PREFIX_CN 环境变量

## 0.0.2-dev.1

### Patch Changes

- feat: 更新 dev IAM_SRV_PREFIX_CN 环境变量

## 0.0.2-dev.0

### Patch Changes

- feat: dev first publish
