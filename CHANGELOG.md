# @iam/init-sdk

## 0.1.0-test.134

### Minor Changes

- feat: 添加基于页面配置的skipShim逻辑

## 0.1.0-test.133

### Patch Changes

- feat: subscribeSuccessCallback

## 0.1.0-test.132

### Patch Changes

- feat: 更改接口路径

## 0.1.0-test.131

### Patch Changes

- feat: 切人

## 0.1.0-test.130

### Patch Changes

- feat: 增加获取 wave Otp 日志

## 0.1.0-test.129

### Patch Changes

- feat: 增加获取 wave Otp 日志

## 0.1.0-test.128

### Patch Changes

- upd: sdk@0.1.0-test.128

## 0.1.0-test.127

### Patch Changes

- upd: sdk@0.1.0-test.127

## 0.1.0-test.126

### Patch Changes

- feat: 日志

## 0.1.0-test.125

### Patch Changes

- feat: 增加日志

## 0.1.0-test.124

### Patch Changes

- upd: sdk@0.1.0-test.124

## 0.1.0-test.123

### Patch Changes

- upd: sdk@0.1.0-test.123

## 0.1.0-test.122

### Patch Changes

- feat: 增加 opCheckLogin

## 0.1.0-test.121

### Patch Changes

- upd: sdk@0.1.0-test.121

## 0.1.0-test.120

### Patch Changes

- upd: sdk@0.1.0-test.120

## 0.1.0-test.119

### Patch Changes

- feat: Op class

## 0.1.0-test.118

### Patch Changes

- fix: is_from_wave 参数类型

## 0.1.0-test.117

### Patch Changes

- feat: 250326

## 0.1.0-test.116

### Patch Changes

- upd: sdk@0.1.0-test.116

## 0.1.0-test.115

### Patch Changes

- upd: sdk@0.1.0-test.115

## 0.1.0-test.114

### Patch Changes

- upd: sdk@0.1.0-test.114

## 0.1.0-test.113

### Patch Changes

- upd: sdk@0.1.0-test.113

## 0.1.0-test.112

### Patch Changes

- upd: sdk@0.1.0-test.112

## 0.1.0-test.111

### Patch Changes

- feat: hoyoverse

## 0.1.0-test.110

### Patch Changes

- upd: sdk@0.1.0-test.110

## 0.1.0-test.109

### Patch Changes

- upd: login@0.1.0-test.109

## 0.1.0-test.108

### Patch Changes

- upd: login@0.1.0-test.108

## 0.1.0-test.107

### Patch Changes

- upd: login@0.1.0-test.107

## 0.1.0-test.106

### Patch Changes

- upd: login@0.1.0-test.106

## 0.1.0-test.105

### Patch Changes

- upd: login@0.1.0-test.105

## 0.1.0-test.104

### Patch Changes

- upd: login@0.1.0-test.104

## 0.1.0-test.103

### Patch Changes

- upd: sdk@0.1.0-test.103

## 0.1.0-test.102

### Patch Changes

- upd: sdk@0.1.0-test.102

## 0.1.0-test.101

### Patch Changes

- upd: sdk@0.1.0-test.101

## 0.1.0-test.100

### Patch Changes

- fix: 类型声明

## 0.1.0-test.99

### Patch Changes

- fix: 类型声明

## 0.1.0-test.98

### Patch Changes

- fix: 路径引用错误

## 0.1.0-test.97

### Patch Changes

- fix: 脚本

## 0.1.0-test.96

### Patch Changes

- upd: sdk@0.1.0-test.96

## 0.1.0-test.95

### Patch Changes

- feat: 不判断 shim

## 0.1.0-test.94

### Patch Changes

- fix: cookie 取值问题

## 0.1.0-test.93

### Minor Changes

- feat: 企微免等也需要 SyncSessionToMoat

## 0.0.2-test.92

### Patch Changes

- feat: 修复 REQUEST_ERROR

## 0.0.2-test.91

### Patch Changes

- fix； extraProps

## 0.0.2-test.90

### Patch Changes

- feat: test

## 0.0.2-test.89

### Patch Changes

- feat: 恢复

## 0.0.2-test.88

### Patch Changes

- feat: rizhi

## 0.0.2-test.87

### Patch Changes

- feat: test

## 0.0.2-test.86

### Patch Changes

- feat: test

## 0.0.2-test.85

### Patch Changes

- feat: test

## 0.0.2-test.84

### Patch Changes

- feat: test

## 0.0.2-test.83

### Patch Changes

- feat: test

## 0.0.2-test.82

### Patch Changes

- feat: ceshi

## 0.0.2-test.81

### Patch Changes

- feat: 兼容老版本 wave 免登

## 0.0.2-test.80

### Patch Changes

- feat: deepEqual 判断 deepEqual

## 0.0.2-test.79

### Patch Changes

- feat: this 指向

## 0.0.2-test.78

### Patch Changes

- feat: bind clientIdManage

## 0.0.2-test.77

### Patch Changes

- feat: 重复执行 直接阻断

## 0.0.2-test.76

### Patch Changes

- feat: 逻辑优化，判断 config 优化渲染

## 0.0.2-test.75

### Patch Changes

- feat: 逻辑优化，判断 config 优化渲染

## 0.0.2-test.74

### Patch Changes

- feat: test unpkg

## 0.0.2-test.73

### Patch Changes

- feat: next unpkg

## 0.0.2-test.72

### Patch Changes

- feat: 删除 sessionCheck

## 0.0.2-test.71

### Patch Changes

- feat: timeout 10000ms

## 0.0.2-test.70

### Patch Changes

- feat: 支持 sessionCheck

## 0.0.2-test.69

### Patch Changes

- fix: 修复在 top 娶不到 clientId 的情况下，返回自身的 clientId

## 0.0.2-test.68

### Patch Changes

- feat: 修改 pp 服务端 domain

## 0.0.2-test.67

### Patch Changes

- feat: 测试 unpkg

## 0.0.2-test.66

### Patch Changes

- feat: unpkg 测试

## 0.0.2-test.65

### Patch Changes

- feat: 内网 unpkg

## 0.0.2-test.64

### Patch Changes

- feat: 测试 unpkg

## 0.0.2-test.63

### Patch Changes

- fix: handleLang

## 0.0.2-test.62

### Patch Changes

- fix: 更改环境变量

## 0.0.2-test.61

### Patch Changes

- feat: 补充 clientId

## 0.0.2-test.60

### Patch Changes

- feat: 补充测试用例

## 0.0.2-test.59

### Patch Changes

- fix: isLogin successCallBack

## 0.0.2-test.58

### Patch Changes

- fix: SyncSessionToMoat 同步成功 没有 return 结果

## 0.0.2-test.57

### Patch Changes

- feat: wave 免登，错误收集上报

## 0.0.2-test.56

### Patch Changes

- feat: 承接 next-app 对于 window 的操作

## 0.0.2-test.55

### Patch Changes

- feat: ie11 兼容

## 0.0.2-test.54

### Patch Changes

- feat: ie11

## 0.0.2-test.32

### Patch Changes

- feat: err 回调

## 0.0.2-test.31

### Patch Changes

- fix: cache

## 0.0.2-test.30

### Patch Changes

- feat: cache

## 0.0.2-test.29

### Patch Changes

- feat: render 过滤环境标识

## 0.0.2-test.28

### Patch Changes

- feat: refresh

## 0.0.2-test.27

### Patch Changes

- feat: test refresh

## 0.0.2-test.26

### Patch Changes

- fix: test IAM_WEBSITE_CN

## 0.0.2-test.25

### Patch Changes

- feat: 测试

## 0.0.2-test.24

### Patch Changes

- feat: test refresh

## 0.0.2-test.23

### Patch Changes

- feat: test refresh

## 0.0.2-test.23

### Patch Changes

- feat:240717

## 0.0.2-test.22

### Patch Changes

- fix: filename

## 0.0.2-test.21

### Patch Changes

- 4387fe7: feat: init test

## 0.0.2-dev.20

### Patch Changes

- b549aa5: feat: test cache

## 0.0.2-dev.19

### Patch Changes

- c3e22f3: feat: test cache

## 0.0.2-dev.18

### Patch Changes

- 7d8063d: fix: cache

## 0.0.2-dev.17

### Patch Changes

- 1050efe: feat: cache

## 0.0.2-dev.16

### Patch Changes

- 5bfd0db: feat: memory cache

## 0.0.2-dev.15

### Patch Changes

- e2019ec: fix: env

## 0.0.2-dev.14

### Patch Changes

- 1f6e263: fix: env

## 0.0.2-dev.13

### Patch Changes

- bb5ba3c: feat: umd name iamNext

## 0.0.2-dev.12

### Patch Changes

- 0bbcc63: feat: test banner

## 0.0.2-dev.11

### Patch Changes

- 8bcfce2: feat: 测试 banner

## 0.0.2-dev.10

### Patch Changes

- 05cdd97: feat: addBanner

## 0.0.2-dev.9

### Patch Changes

- cb77738: feat: test umd

## 0.0.2-dev.8

### Patch Changes

- 74bf52b: feat: test umd

## 0.0.2-dev.7

### Patch Changes

- c150fbb: feat: 测试 umd

## 0.0.2-dev.6

### Patch Changes

- fccc12e: fix: 修复 destroy 时序错误

## 0.0.2-dev.5

### Patch Changes

- 03cd7f2: feat: 新增 iframe destroy 函数，回调结束后 destroy

## 0.0.2-dev.4

### Patch Changes

- feat: 删除 session/check 逻辑

## 0.0.2-dev.3

### Patch Changes

- Update .env.dev

## 0.0.2-dev.2

### Patch Changes

- feat: 更新 test IAM_SRV_PREFIX_CN 环境变量

## 0.0.2-dev.1

### Patch Changes

- feat: 更新 dev IAM_SRV_PREFIX_CN 环境变量

## 0.0.2-dev.0

### Patch Changes

- feat: dev first publish
