{"mode": "pre", "tag": "test", "initialVersions": {"@iam/init-sdk": "0.0.2-dev.20"}, "changesets": ["afraid-coins-rest", "blue-forks-pull", "breezy-tables-carry", "breezy-toys-deliver", "breezy-waves-shake", "bright-turkeys-lie", "chilled-mangos-listen", "chilly-carrots-turn", "clean-kangaroos-crash", "clean-pans-sip", "cold-waves-push", "cool-parents-add", "cuddly-mice-run", "cuddly-owls-change", "cyan-pillows-agree", "dull-peas-peel", "empty-rockets-swim", "famous-oranges-cross", "fuzzy-years-sin", "giant-readers-wave", "giant-tomatoes-wave", "good-cameras-run", "good-pants-rush", "gorgeous-cycles-divide", "great-balloons-clap", "great-penguins-smash", "great-radios-notice", "hip-carpets-relax", "hungry-owls-scream", "hungry-suits-clean", "khaki-starfishes-enjoy", "late-taxis-appear", "lemon-rockets-breathe", "little-pigs-return", "loud-beans-smash", "lovely-planes-applaud", "many-rings-jam", "mean-guests-poke", "metal-flowers-sip", "modern-islands-add", "modern-paws-hammer", "moody-bees-poke", "neat-buses-knock", "new-emus-camp", "nice-books-provide", "nice-paws-work", "old-hotels-nail", "plenty-pets-juggle", "plenty-pugs-tease", "plenty-trains-cough", "poor-hats-smell", "pretty-dolls-carry", "rare-eagles-smell", "real-papayas-sort", "red-brooms-taste", "rotten-birds-travel", "rotten-nails-glow", "rude-mails-hunt", "serious-cherries-look", "serious-poems-shout", "sharp-wasps-act", "shy-planes-jog", "slimy-coins-peel", "soft-ants-smile", "sour-adults-talk", "spicy-apples-refuse", "stale-weeks-return", "strange-parrots-serve", "tame-donkeys-explain", "tender-dodos-serve", "thick-adults-tell", "three-hornets-perform", "tidy-phones-laugh", "tiny-worms-shout", "twenty-planets-boil", "violet-guests-lie", "wet-spies-yell", "wise-lemons-taste", "witty-ties-breathe"]}