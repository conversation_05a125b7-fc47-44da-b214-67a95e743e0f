import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";


export default [
  {files: ["src/**/*.ts"]},
  {ignores: ["dist/*", ".prettierrc.js", "rollup.config.js", "jest.config.ts", "jest.setup.ts"]},
  {languageOptions: {globals: globals.browser}},
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    }
  }
];
